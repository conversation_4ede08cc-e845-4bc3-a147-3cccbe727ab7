const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/dagre-OKDRZEBW-g_0ATcVB.js","assets/graph-CB2qILXY.js","assets/_baseUniq-CvCjC6qE.js","assets/layout-BntSLW4t.js","assets/_basePickBy-DY0K0qir.js","assets/clone-CFB5BEXb.js","assets/feature-graph-ChwGSzXI.js","assets/react-vendor-DEwriMA6.js","assets/graph-vendor-B-X5JegA.js","assets/ui-vendor-CeCm8EER.js","assets/utils-vendor-BysuhMZA.js","assets/feature-graph-BipNuM18.css","assets/c4Diagram-VJAJSXHY-szasl701.js","assets/chunk-D6G4REZN-Ct1-6Dc1.js","assets/flowDiagram-4HSFHLVR-BdHPhGfE.js","assets/chunk-RZ5BOZE2-DCsogQCZ.js","assets/erDiagram-Q7BY3M3F-DLPiuuPL.js","assets/gitGraphDiagram-7IBYFJ6S-D24Y9tf4.js","assets/chunk-4BMEZGHF-BSIbM2yC.js","assets/chunk-XZIHB7SX-CaSSt3CB.js","assets/radar-MK3ICKWK-CxtTBuu1.js","assets/ganttDiagram-APWFNJXF-DXTK-VH-.js","assets/infoDiagram-PH2N3AL5-j82a-EEt.js","assets/pieDiagram-IB7DONF6-CJylfmA9.js","assets/quadrantDiagram-7GDLP6J5-DzEP7PZZ.js","assets/xychartDiagram-VJFVF3MP-C0MyBtk4.js","assets/requirementDiagram-KVF5MWMF-DhduwYYL.js","assets/sequenceDiagram-X6HHIX6F-BdyORR2w.js","assets/classDiagram-GIVACNV2-CEOXQUJC.js","assets/chunk-A2AXSNBT-Cn1JMUWB.js","assets/classDiagram-v2-COTLJTTW-CEOXQUJC.js","assets/stateDiagram-DGXRK772-CayFiP2j.js","assets/chunk-AEK57VVT-DDDSE95R.js","assets/stateDiagram-v2-YXO3MK2T-Dc8Qo_WT.js","assets/journeyDiagram-U35MCT3I-WaCoTE8u.js","assets/timeline-definition-BDJGKUSR-BDjmla0M.js","assets/mindmap-definition-ALO5MXBD-Du6ihiNd.js","assets/cytoscape.esm-CfBqOv7Q.js","assets/kanban-definition-NDS4AKOZ-BBaWRXqD.js","assets/sankeyDiagram-QLVOVGJD-CdgNy4-D.js","assets/diagram-VNBRO52H-CE0NmFgN.js","assets/diagram-SSKATNLV-CQi1uaEl.js","assets/blockDiagram-JOT3LUYC-j6QY8oGG.js","assets/architectureDiagram-IEHRJDOE-By1r-_vc.js"])))=>i.map(i=>d[i]);
var qy=Object.defineProperty;var Hy=(e,t,r)=>t in e?qy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Ct=(e,t,r)=>Hy(e,typeof t!="symbol"?t+"":t,r);import{a2 as _t}from"./feature-graph-ChwGSzXI.js";import{g as Uy}from"./react-vendor-DEwriMA6.js";var na={exports:{}},Yy=na.exports,Kc;function jy(){return Kc||(Kc=1,function(e,t){(function(r,i){e.exports=i()})(Yy,function(){var r=1e3,i=6e4,n=36e5,a="millisecond",o="second",s="minute",c="hour",l="day",h="week",u="month",f="quarter",d="year",p="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(A){var F=["th","st","nd","rd"],L=A%100;return"["+A+(F[(L-20)%10]||F[L]||F[0])+"]"}},C=function(A,F,L){var D=String(A);return!D||D.length>=F?A:""+Array(F+1-D.length).join(L)+A},v={s:C,z:function(A){var F=-A.utcOffset(),L=Math.abs(F),D=Math.floor(L/60),B=L%60;return(F<=0?"+":"-")+C(D,2,"0")+":"+C(B,2,"0")},m:function A(F,L){if(F.date()<L.date())return-A(L,F);var D=12*(L.year()-F.year())+(L.month()-F.month()),B=F.clone().add(D,u),W=L-B<0,U=F.clone().add(D+(W?-1:1),u);return+(-(D+(L-B)/(W?B-U:U-B))||0)},a:function(A){return A<0?Math.ceil(A)||0:Math.floor(A)},p:function(A){return{M:u,y:d,w:h,d:l,D:p,h:c,m:s,s:o,ms:a,Q:f}[A]||String(A||"").toLowerCase().replace(/s$/,"")},u:function(A){return A===void 0}},k="en",_={};_[k]=b;var w="$isDayjsObject",O=function(A){return A instanceof I||!(!A||!A[w])},N=function A(F,L,D){var B;if(!F)return k;if(typeof F=="string"){var W=F.toLowerCase();_[W]&&(B=W),L&&(_[W]=L,B=W);var U=F.split("-");if(!B&&U.length>1)return A(U[0])}else{var X=F.name;_[X]=F,B=X}return!D&&B&&(k=B),B||!D&&k},$=function(A,F){if(O(A))return A.clone();var L=typeof F=="object"?F:{};return L.date=A,L.args=arguments,new I(L)},S=v;S.l=N,S.i=O,S.w=function(A,F){return $(A,{locale:F.$L,utc:F.$u,x:F.$x,$offset:F.$offset})};var I=function(){function A(L){this.$L=N(L.locale,null,!0),this.parse(L),this.$x=this.$x||L.x||{},this[w]=!0}var F=A.prototype;return F.parse=function(L){this.$d=function(D){var B=D.date,W=D.utc;if(B===null)return new Date(NaN);if(S.u(B))return new Date;if(B instanceof Date)return new Date(B);if(typeof B=="string"&&!/Z$/i.test(B)){var U=B.match(y);if(U){var X=U[2]-1||0,J=(U[7]||"0").substring(0,3);return W?new Date(Date.UTC(U[1],X,U[3]||1,U[4]||0,U[5]||0,U[6]||0,J)):new Date(U[1],X,U[3]||1,U[4]||0,U[5]||0,U[6]||0,J)}}return new Date(B)}(L),this.init()},F.init=function(){var L=this.$d;this.$y=L.getFullYear(),this.$M=L.getMonth(),this.$D=L.getDate(),this.$W=L.getDay(),this.$H=L.getHours(),this.$m=L.getMinutes(),this.$s=L.getSeconds(),this.$ms=L.getMilliseconds()},F.$utils=function(){return S},F.isValid=function(){return this.$d.toString()!==m},F.isSame=function(L,D){var B=$(L);return this.startOf(D)<=B&&B<=this.endOf(D)},F.isAfter=function(L,D){return $(L)<this.startOf(D)},F.isBefore=function(L,D){return this.endOf(D)<$(L)},F.$g=function(L,D,B){return S.u(L)?this[D]:this.set(B,L)},F.unix=function(){return Math.floor(this.valueOf()/1e3)},F.valueOf=function(){return this.$d.getTime()},F.startOf=function(L,D){var B=this,W=!!S.u(D)||D,U=S.p(L),X=function(q,G){var ct=S.w(B.$u?Date.UTC(B.$y,G,q):new Date(B.$y,G,q),B);return W?ct:ct.endOf(l)},J=function(q,G){return S.w(B.toDate()[q].apply(B.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(G)),B)},it=this.$W,ut=this.$M,Q=this.$D,kt="set"+(this.$u?"UTC":"");switch(U){case d:return W?X(1,0):X(31,11);case u:return W?X(1,ut):X(0,ut+1);case h:var Tt=this.$locale().weekStart||0,It=(it<Tt?it+7:it)-Tt;return X(W?Q-It:Q+(6-It),ut);case l:case p:return J(kt+"Hours",0);case c:return J(kt+"Minutes",1);case s:return J(kt+"Seconds",2);case o:return J(kt+"Milliseconds",3);default:return this.clone()}},F.endOf=function(L){return this.startOf(L,!1)},F.$set=function(L,D){var B,W=S.p(L),U="set"+(this.$u?"UTC":""),X=(B={},B[l]=U+"Date",B[p]=U+"Date",B[u]=U+"Month",B[d]=U+"FullYear",B[c]=U+"Hours",B[s]=U+"Minutes",B[o]=U+"Seconds",B[a]=U+"Milliseconds",B)[W],J=W===l?this.$D+(D-this.$W):D;if(W===u||W===d){var it=this.clone().set(p,1);it.$d[X](J),it.init(),this.$d=it.set(p,Math.min(this.$D,it.daysInMonth())).$d}else X&&this.$d[X](J);return this.init(),this},F.set=function(L,D){return this.clone().$set(L,D)},F.get=function(L){return this[S.p(L)]()},F.add=function(L,D){var B,W=this;L=Number(L);var U=S.p(D),X=function(ut){var Q=$(W);return S.w(Q.date(Q.date()+Math.round(ut*L)),W)};if(U===u)return this.set(u,this.$M+L);if(U===d)return this.set(d,this.$y+L);if(U===l)return X(1);if(U===h)return X(7);var J=(B={},B[s]=i,B[c]=n,B[o]=r,B)[U]||1,it=this.$d.getTime()+L*J;return S.w(it,this)},F.subtract=function(L,D){return this.add(-1*L,D)},F.format=function(L){var D=this,B=this.$locale();if(!this.isValid())return B.invalidDate||m;var W=L||"YYYY-MM-DDTHH:mm:ssZ",U=S.z(this),X=this.$H,J=this.$m,it=this.$M,ut=B.weekdays,Q=B.months,kt=B.meridiem,Tt=function(G,ct,P,Mt){return G&&(G[ct]||G(D,W))||P[ct].slice(0,Mt)},It=function(G){return S.s(X%12||12,G,"0")},q=kt||function(G,ct,P){var Mt=G<12?"AM":"PM";return P?Mt.toLowerCase():Mt};return W.replace(x,function(G,ct){return ct||function(P){switch(P){case"YY":return String(D.$y).slice(-2);case"YYYY":return S.s(D.$y,4,"0");case"M":return it+1;case"MM":return S.s(it+1,2,"0");case"MMM":return Tt(B.monthsShort,it,Q,3);case"MMMM":return Tt(Q,it);case"D":return D.$D;case"DD":return S.s(D.$D,2,"0");case"d":return String(D.$W);case"dd":return Tt(B.weekdaysMin,D.$W,ut,2);case"ddd":return Tt(B.weekdaysShort,D.$W,ut,3);case"dddd":return ut[D.$W];case"H":return String(X);case"HH":return S.s(X,2,"0");case"h":return It(1);case"hh":return It(2);case"a":return q(X,J,!0);case"A":return q(X,J,!1);case"m":return String(J);case"mm":return S.s(J,2,"0");case"s":return String(D.$s);case"ss":return S.s(D.$s,2,"0");case"SSS":return S.s(D.$ms,3,"0");case"Z":return U}return null}(G)||U.replace(":","")})},F.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},F.diff=function(L,D,B){var W,U=this,X=S.p(D),J=$(L),it=(J.utcOffset()-this.utcOffset())*i,ut=this-J,Q=function(){return S.m(U,J)};switch(X){case d:W=Q()/12;break;case u:W=Q();break;case f:W=Q()/3;break;case h:W=(ut-it)/6048e5;break;case l:W=(ut-it)/864e5;break;case c:W=ut/n;break;case s:W=ut/i;break;case o:W=ut/r;break;default:W=ut}return B?W:S.a(W)},F.daysInMonth=function(){return this.endOf(u).$D},F.$locale=function(){return _[this.$L]},F.locale=function(L,D){if(!L)return this.$L;var B=this.clone(),W=N(L,D,!0);return W&&(B.$L=W),B},F.clone=function(){return S.w(this.$d,this)},F.toDate=function(){return new Date(this.valueOf())},F.toJSON=function(){return this.isValid()?this.toISOString():null},F.toISOString=function(){return this.$d.toISOString()},F.toString=function(){return this.$d.toUTCString()},A}(),E=I.prototype;return $.prototype=E,[["$ms",a],["$s",o],["$m",s],["$H",c],["$W",l],["$M",u],["$y",d],["$D",p]].forEach(function(A){E[A[1]]=function(F){return this.$g(F,A[0],A[1])}}),$.extend=function(A,F){return A.$i||(A(F,I,$),A.$i=!0),$},$.locale=N,$.isDayjs=O,$.unix=function(A){return $(1e3*A)},$.en=_[k],$.Ls=_,$.p={},$})}(na)),na.exports}var Gy=jy();const Vy=Uy(Gy),aa={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;switch(i){case"r":return aa.hue2rgb(a,n,e+1/3)*255;case"g":return aa.hue2rgb(a,n,e)*255;case"b":return aa.hue2rgb(a,n,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const n=Math.max(e,t,r),a=Math.min(e,t,r),o=(n+a)/2;if(i==="l")return o*100;if(n===a)return 0;const s=n-a,c=o>.5?s/(2-n-a):s/(n+a);if(i==="s")return c*100;switch(n){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},Xy={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},Zy={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},lt={channel:aa,lang:Xy,unit:Zy},ar={};for(let e=0;e<=255;e++)ar[e]=lt.unit.dec2hex(e);const jt={ALL:0,RGB:1,HSL:2};class Ky{constructor(){this.type=jt.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=jt.ALL}is(t){return this.type===t}}class Qy{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new Ky}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=jt.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:n}=t;r===void 0&&(t.h=lt.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=lt.channel.rgb2hsl(t,"s")),n===void 0&&(t.l=lt.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:n}=t;r===void 0&&(t.r=lt.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=lt.channel.hsl2rgb(t,"g")),n===void 0&&(t.b=lt.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is(jt.HSL)&&r!==void 0?r:(this._ensureHSL(),lt.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is(jt.HSL)&&r!==void 0?r:(this._ensureHSL(),lt.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is(jt.HSL)&&r!==void 0?r:(this._ensureHSL(),lt.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is(jt.RGB)&&r!==void 0?r:(this._ensureRGB(),lt.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is(jt.RGB)&&r!==void 0?r:(this._ensureRGB(),lt.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is(jt.RGB)&&r!==void 0?r:(this._ensureRGB(),lt.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set(jt.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(jt.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(jt.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(jt.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(jt.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(jt.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const fs=new Qy({r:0,g:0,b:0,a:0},"transparent"),ri={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(ri.re);if(!t)return;const r=t[1],i=parseInt(r,16),n=r.length,a=n%4===0,o=n>4,s=o?1:17,c=o?8:4,l=a?0:-1,h=o?255:15;return fs.set({r:(i>>c*(l+3)&h)*s,g:(i>>c*(l+2)&h)*s,b:(i>>c*(l+1)&h)*s,a:a?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a:n}=e;return n<1?`#${ar[Math.round(t)]}${ar[Math.round(r)]}${ar[Math.round(i)]}${ar[Math.round(n*255)]}`:`#${ar[Math.round(t)]}${ar[Math.round(r)]}${ar[Math.round(i)]}`}},Cr={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Cr.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return lt.channel.clamp.h(parseFloat(r)*.9);case"rad":return lt.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return lt.channel.clamp.h(parseFloat(r)*360)}}return lt.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Cr.re);if(!r)return;const[,i,n,a,o,s]=r;return fs.set({h:Cr._hue2deg(i),s:lt.channel.clamp.s(parseFloat(n)),l:lt.channel.clamp.l(parseFloat(a)),a:o?lt.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a:n}=e;return n<1?`hsla(${lt.lang.round(t)}, ${lt.lang.round(r)}%, ${lt.lang.round(i)}%, ${n})`:`hsl(${lt.lang.round(t)}, ${lt.lang.round(r)}%, ${lt.lang.round(i)}%)`}},on={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=on.colors[e];if(t)return ri.parse(t)},stringify:e=>{const t=ri.stringify(e);for(const r in on.colors)if(on.colors[r]===t)return r}},Ji={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(Ji.re);if(!r)return;const[,i,n,a,o,s,c,l,h]=r;return fs.set({r:lt.channel.clamp.r(n?parseFloat(i)*2.55:parseFloat(i)),g:lt.channel.clamp.g(o?parseFloat(a)*2.55:parseFloat(a)),b:lt.channel.clamp.b(c?parseFloat(s)*2.55:parseFloat(s)),a:l?lt.channel.clamp.a(h?parseFloat(l)/100:parseFloat(l)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a:n}=e;return n<1?`rgba(${lt.lang.round(t)}, ${lt.lang.round(r)}, ${lt.lang.round(i)}, ${lt.lang.round(n)})`:`rgb(${lt.lang.round(t)}, ${lt.lang.round(r)}, ${lt.lang.round(i)})`}},Te={format:{keyword:on,hex:ri,rgb:Ji,rgba:Ji,hsl:Cr,hsla:Cr},parse:e=>{if(typeof e!="string")return e;const t=ri.parse(e)||Ji.parse(e)||Cr.parse(e)||on.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is(jt.HSL)||e.data.r===void 0?Cr.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?Ji.stringify(e):ri.stringify(e)},Ku=(e,t)=>{const r=Te.parse(e);for(const i in t)r[i]=lt.channel.clamp[i](t[i]);return Te.stringify(r)},ln=(e,t,r=0,i=1)=>{if(typeof e!="number")return Ku(e,{a:t});const n=fs.set({r:lt.channel.clamp.r(e),g:lt.channel.clamp.g(t),b:lt.channel.clamp.b(r),a:lt.channel.clamp.a(i)});return Te.stringify(n)},a3=(e,t)=>lt.lang.round(Te.parse(e)[t]),Jy=e=>{const{r:t,g:r,b:i}=Te.parse(e),n=.2126*lt.channel.toLinear(t)+.7152*lt.channel.toLinear(r)+.0722*lt.channel.toLinear(i);return lt.lang.round(n)},tx=e=>Jy(e)>=.5,Mn=e=>!tx(e),Qu=(e,t,r)=>{const i=Te.parse(e),n=i[t],a=lt.channel.clamp[t](n+r);return n!==a&&(i[t]=a),Te.stringify(i)},Y=(e,t)=>Qu(e,"l",t),at=(e,t)=>Qu(e,"l",-t),M=(e,t)=>{const r=Te.parse(e),i={};for(const n in t)t[n]&&(i[n]=r[n]+t[n]);return Ku(e,i)},ex=(e,t,r=50)=>{const{r:i,g:n,b:a,a:o}=Te.parse(e),{r:s,g:c,b:l,a:h}=Te.parse(t),u=r/100,f=u*2-1,d=o-h,m=((f*d===-1?f:(f+d)/(1+f*d))+1)/2,y=1-m,x=i*m+s*y,b=n*m+c*y,C=a*m+l*y,v=o*u+h*(1-u);return ln(x,b,C,v)},H=(e,t=100)=>{const r=Te.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,ex(r,e,t)};/*! @license DOMPurify 3.2.5 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE */const{entries:Ju,setPrototypeOf:Qc,isFrozen:rx,getPrototypeOf:ix,getOwnPropertyDescriptor:nx}=Object;let{freeze:ie,seal:be,create:tf}=Object,{apply:Mo,construct:Ao}=typeof Reflect<"u"&&Reflect;ie||(ie=function(t){return t});be||(be=function(t){return t});Mo||(Mo=function(t,r,i){return t.apply(r,i)});Ao||(Ao=function(t,r){return new t(...r)});const Un=ne(Array.prototype.forEach),ax=ne(Array.prototype.lastIndexOf),Jc=ne(Array.prototype.pop),Ii=ne(Array.prototype.push),sx=ne(Array.prototype.splice),sa=ne(String.prototype.toLowerCase),Xs=ne(String.prototype.toString),th=ne(String.prototype.match),Pi=ne(String.prototype.replace),ox=ne(String.prototype.indexOf),lx=ne(String.prototype.trim),Ce=ne(Object.prototype.hasOwnProperty),Qt=ne(RegExp.prototype.test),Ni=cx(TypeError);function ne(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return Mo(e,t,i)}}function cx(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return Ao(e,r)}}function ft(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:sa;Qc&&Qc(e,null);let i=t.length;for(;i--;){let n=t[i];if(typeof n=="string"){const a=r(n);a!==n&&(rx(t)||(t[i]=a),n=a)}e[n]=!0}return e}function hx(e){for(let t=0;t<e.length;t++)Ce(e,t)||(e[t]=null);return e}function yr(e){const t=tf(null);for(const[r,i]of Ju(e))Ce(e,r)&&(Array.isArray(i)?t[r]=hx(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=yr(i):t[r]=i);return t}function zi(e,t){for(;e!==null;){const i=nx(e,t);if(i){if(i.get)return ne(i.get);if(typeof i.value=="function")return ne(i.value)}e=ix(e)}function r(){return null}return r}const eh=ie(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Zs=ie(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ks=ie(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ux=ie(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Qs=ie(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),fx=ie(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),rh=ie(["#text"]),ih=ie(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Js=ie(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),nh=ie(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Yn=ie(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),dx=be(/\{\{[\w\W]*|[\w\W]*\}\}/gm),px=be(/<%[\w\W]*|[\w\W]*%>/gm),gx=be(/\$\{[\w\W]*/gm),mx=be(/^data-[\-\w.\u00B7-\uFFFF]+$/),yx=be(/^aria-[\-\w]+$/),ef=be(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),xx=be(/^(?:\w+script|data):/i),bx=be(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),rf=be(/^html$/i),_x=be(/^[a-z][.\w]*(-[.\w]+)+$/i);var ah=Object.freeze({__proto__:null,ARIA_ATTR:yx,ATTR_WHITESPACE:bx,CUSTOM_ELEMENT:_x,DATA_ATTR:mx,DOCTYPE_NAME:rf,ERB_EXPR:px,IS_ALLOWED_URI:ef,IS_SCRIPT_OR_DATA:xx,MUSTACHE_EXPR:dx,TMPLIT_EXPR:gx});const Wi={element:1,text:3,progressingInstruction:7,comment:8,document:9},Cx=function(){return typeof window>"u"?null:window},wx=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const n="data-tt-policy-suffix";r&&r.hasAttribute(n)&&(i=r.getAttribute(n));const a="dompurify"+(i?"#"+i:"");try{return t.createPolicy(a,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},sh=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function nf(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Cx();const t=nt=>nf(nt);if(t.version="3.2.5",t.removed=[],!e||!e.document||e.document.nodeType!==Wi.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,n=i.currentScript,{DocumentFragment:a,HTMLTemplateElement:o,Node:s,Element:c,NodeFilter:l,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:f,trustedTypes:d}=e,p=c.prototype,m=zi(p,"cloneNode"),y=zi(p,"remove"),x=zi(p,"nextSibling"),b=zi(p,"childNodes"),C=zi(p,"parentNode");if(typeof o=="function"){const nt=r.createElement("template");nt.content&&nt.content.ownerDocument&&(r=nt.content.ownerDocument)}let v,k="";const{implementation:_,createNodeIterator:w,createDocumentFragment:O,getElementsByTagName:N}=r,{importNode:$}=i;let S=sh();t.isSupported=typeof Ju=="function"&&typeof C=="function"&&_&&_.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:I,ERB_EXPR:E,TMPLIT_EXPR:A,DATA_ATTR:F,ARIA_ATTR:L,IS_SCRIPT_OR_DATA:D,ATTR_WHITESPACE:B,CUSTOM_ELEMENT:W}=ah;let{IS_ALLOWED_URI:U}=ah,X=null;const J=ft({},[...eh,...Zs,...Ks,...Qs,...rh]);let it=null;const ut=ft({},[...ih,...Js,...nh,...Yn]);let Q=Object.seal(tf(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),kt=null,Tt=null,It=!0,q=!0,G=!1,ct=!0,P=!1,Mt=!0,dt=!1,Pt=!1,Nt=!1,ae=!1,pr=!1,Pn=!1,$c=!0,Dc=!1;const Dy="user-content-";let Hs=!0,Di=!1,Ur={},Yr=null;const Oc=ft({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Rc=null;const Ic=ft({},["audio","video","img","source","image","track"]);let Us=null;const Pc=ft({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Nn="http://www.w3.org/1998/Math/MathML",zn="http://www.w3.org/2000/svg",ze="http://www.w3.org/1999/xhtml";let jr=ze,Ys=!1,js=null;const Oy=ft({},[Nn,zn,ze],Xs);let Wn=ft({},["mi","mo","mn","ms","mtext"]),qn=ft({},["annotation-xml"]);const Ry=ft({},["title","style","font","a","script"]);let Oi=null;const Iy=["application/xhtml+xml","text/html"],Py="text/html";let Ot=null,Gr=null;const Ny=r.createElement("form"),Nc=function(T){return T instanceof RegExp||T instanceof Function},Gs=function(){let T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Gr&&Gr===T)){if((!T||typeof T!="object")&&(T={}),T=yr(T),Oi=Iy.indexOf(T.PARSER_MEDIA_TYPE)===-1?Py:T.PARSER_MEDIA_TYPE,Ot=Oi==="application/xhtml+xml"?Xs:sa,X=Ce(T,"ALLOWED_TAGS")?ft({},T.ALLOWED_TAGS,Ot):J,it=Ce(T,"ALLOWED_ATTR")?ft({},T.ALLOWED_ATTR,Ot):ut,js=Ce(T,"ALLOWED_NAMESPACES")?ft({},T.ALLOWED_NAMESPACES,Xs):Oy,Us=Ce(T,"ADD_URI_SAFE_ATTR")?ft(yr(Pc),T.ADD_URI_SAFE_ATTR,Ot):Pc,Rc=Ce(T,"ADD_DATA_URI_TAGS")?ft(yr(Ic),T.ADD_DATA_URI_TAGS,Ot):Ic,Yr=Ce(T,"FORBID_CONTENTS")?ft({},T.FORBID_CONTENTS,Ot):Oc,kt=Ce(T,"FORBID_TAGS")?ft({},T.FORBID_TAGS,Ot):{},Tt=Ce(T,"FORBID_ATTR")?ft({},T.FORBID_ATTR,Ot):{},Ur=Ce(T,"USE_PROFILES")?T.USE_PROFILES:!1,It=T.ALLOW_ARIA_ATTR!==!1,q=T.ALLOW_DATA_ATTR!==!1,G=T.ALLOW_UNKNOWN_PROTOCOLS||!1,ct=T.ALLOW_SELF_CLOSE_IN_ATTR!==!1,P=T.SAFE_FOR_TEMPLATES||!1,Mt=T.SAFE_FOR_XML!==!1,dt=T.WHOLE_DOCUMENT||!1,ae=T.RETURN_DOM||!1,pr=T.RETURN_DOM_FRAGMENT||!1,Pn=T.RETURN_TRUSTED_TYPE||!1,Nt=T.FORCE_BODY||!1,$c=T.SANITIZE_DOM!==!1,Dc=T.SANITIZE_NAMED_PROPS||!1,Hs=T.KEEP_CONTENT!==!1,Di=T.IN_PLACE||!1,U=T.ALLOWED_URI_REGEXP||ef,jr=T.NAMESPACE||ze,Wn=T.MATHML_TEXT_INTEGRATION_POINTS||Wn,qn=T.HTML_INTEGRATION_POINTS||qn,Q=T.CUSTOM_ELEMENT_HANDLING||{},T.CUSTOM_ELEMENT_HANDLING&&Nc(T.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Q.tagNameCheck=T.CUSTOM_ELEMENT_HANDLING.tagNameCheck),T.CUSTOM_ELEMENT_HANDLING&&Nc(T.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Q.attributeNameCheck=T.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),T.CUSTOM_ELEMENT_HANDLING&&typeof T.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Q.allowCustomizedBuiltInElements=T.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),P&&(q=!1),pr&&(ae=!0),Ur&&(X=ft({},rh),it=[],Ur.html===!0&&(ft(X,eh),ft(it,ih)),Ur.svg===!0&&(ft(X,Zs),ft(it,Js),ft(it,Yn)),Ur.svgFilters===!0&&(ft(X,Ks),ft(it,Js),ft(it,Yn)),Ur.mathMl===!0&&(ft(X,Qs),ft(it,nh),ft(it,Yn))),T.ADD_TAGS&&(X===J&&(X=yr(X)),ft(X,T.ADD_TAGS,Ot)),T.ADD_ATTR&&(it===ut&&(it=yr(it)),ft(it,T.ADD_ATTR,Ot)),T.ADD_URI_SAFE_ATTR&&ft(Us,T.ADD_URI_SAFE_ATTR,Ot),T.FORBID_CONTENTS&&(Yr===Oc&&(Yr=yr(Yr)),ft(Yr,T.FORBID_CONTENTS,Ot)),Hs&&(X["#text"]=!0),dt&&ft(X,["html","head","body"]),X.table&&(ft(X,["tbody"]),delete kt.tbody),T.TRUSTED_TYPES_POLICY){if(typeof T.TRUSTED_TYPES_POLICY.createHTML!="function")throw Ni('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof T.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Ni('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');v=T.TRUSTED_TYPES_POLICY,k=v.createHTML("")}else v===void 0&&(v=wx(d,n)),v!==null&&typeof k=="string"&&(k=v.createHTML(""));ie&&ie(T),Gr=T}},zc=ft({},[...Zs,...Ks,...ux]),Wc=ft({},[...Qs,...fx]),zy=function(T){let z=C(T);(!z||!z.tagName)&&(z={namespaceURI:jr,tagName:"template"});const Z=sa(T.tagName),vt=sa(z.tagName);return js[T.namespaceURI]?T.namespaceURI===zn?z.namespaceURI===ze?Z==="svg":z.namespaceURI===Nn?Z==="svg"&&(vt==="annotation-xml"||Wn[vt]):!!zc[Z]:T.namespaceURI===Nn?z.namespaceURI===ze?Z==="math":z.namespaceURI===zn?Z==="math"&&qn[vt]:!!Wc[Z]:T.namespaceURI===ze?z.namespaceURI===zn&&!qn[vt]||z.namespaceURI===Nn&&!Wn[vt]?!1:!Wc[Z]&&(Ry[Z]||!zc[Z]):!!(Oi==="application/xhtml+xml"&&js[T.namespaceURI]):!1},Ae=function(T){Ii(t.removed,{element:T});try{C(T).removeChild(T)}catch{y(T)}},Hn=function(T,z){try{Ii(t.removed,{attribute:z.getAttributeNode(T),from:z})}catch{Ii(t.removed,{attribute:null,from:z})}if(z.removeAttribute(T),T==="is")if(ae||pr)try{Ae(z)}catch{}else try{z.setAttribute(T,"")}catch{}},qc=function(T){let z=null,Z=null;if(Nt)T="<remove></remove>"+T;else{const zt=th(T,/^[\r\n\t ]+/);Z=zt&&zt[0]}Oi==="application/xhtml+xml"&&jr===ze&&(T='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+T+"</body></html>");const vt=v?v.createHTML(T):T;if(jr===ze)try{z=new f().parseFromString(vt,Oi)}catch{}if(!z||!z.documentElement){z=_.createDocument(jr,"template",null);try{z.documentElement.innerHTML=Ys?k:vt}catch{}}const Ut=z.body||z.documentElement;return T&&Z&&Ut.insertBefore(r.createTextNode(Z),Ut.childNodes[0]||null),jr===ze?N.call(z,dt?"html":"body")[0]:dt?z.documentElement:Ut},Hc=function(T){return w.call(T.ownerDocument||T,T,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},Vs=function(T){return T instanceof u&&(typeof T.nodeName!="string"||typeof T.textContent!="string"||typeof T.removeChild!="function"||!(T.attributes instanceof h)||typeof T.removeAttribute!="function"||typeof T.setAttribute!="function"||typeof T.namespaceURI!="string"||typeof T.insertBefore!="function"||typeof T.hasChildNodes!="function")},Uc=function(T){return typeof s=="function"&&T instanceof s};function We(nt,T,z){Un(nt,Z=>{Z.call(t,T,z,Gr)})}const Yc=function(T){let z=null;if(We(S.beforeSanitizeElements,T,null),Vs(T))return Ae(T),!0;const Z=Ot(T.nodeName);if(We(S.uponSanitizeElement,T,{tagName:Z,allowedTags:X}),T.hasChildNodes()&&!Uc(T.firstElementChild)&&Qt(/<[/\w!]/g,T.innerHTML)&&Qt(/<[/\w!]/g,T.textContent)||T.nodeType===Wi.progressingInstruction||Mt&&T.nodeType===Wi.comment&&Qt(/<[/\w]/g,T.data))return Ae(T),!0;if(!X[Z]||kt[Z]){if(!kt[Z]&&Gc(Z)&&(Q.tagNameCheck instanceof RegExp&&Qt(Q.tagNameCheck,Z)||Q.tagNameCheck instanceof Function&&Q.tagNameCheck(Z)))return!1;if(Hs&&!Yr[Z]){const vt=C(T)||T.parentNode,Ut=b(T)||T.childNodes;if(Ut&&vt){const zt=Ut.length;for(let se=zt-1;se>=0;--se){const Le=m(Ut[se],!0);Le.__removalCount=(T.__removalCount||0)+1,vt.insertBefore(Le,x(T))}}}return Ae(T),!0}return T instanceof c&&!zy(T)||(Z==="noscript"||Z==="noembed"||Z==="noframes")&&Qt(/<\/no(script|embed|frames)/i,T.innerHTML)?(Ae(T),!0):(P&&T.nodeType===Wi.text&&(z=T.textContent,Un([I,E,A],vt=>{z=Pi(z,vt," ")}),T.textContent!==z&&(Ii(t.removed,{element:T.cloneNode()}),T.textContent=z)),We(S.afterSanitizeElements,T,null),!1)},jc=function(T,z,Z){if($c&&(z==="id"||z==="name")&&(Z in r||Z in Ny))return!1;if(!(q&&!Tt[z]&&Qt(F,z))){if(!(It&&Qt(L,z))){if(!it[z]||Tt[z]){if(!(Gc(T)&&(Q.tagNameCheck instanceof RegExp&&Qt(Q.tagNameCheck,T)||Q.tagNameCheck instanceof Function&&Q.tagNameCheck(T))&&(Q.attributeNameCheck instanceof RegExp&&Qt(Q.attributeNameCheck,z)||Q.attributeNameCheck instanceof Function&&Q.attributeNameCheck(z))||z==="is"&&Q.allowCustomizedBuiltInElements&&(Q.tagNameCheck instanceof RegExp&&Qt(Q.tagNameCheck,Z)||Q.tagNameCheck instanceof Function&&Q.tagNameCheck(Z))))return!1}else if(!Us[z]){if(!Qt(U,Pi(Z,B,""))){if(!((z==="src"||z==="xlink:href"||z==="href")&&T!=="script"&&ox(Z,"data:")===0&&Rc[T])){if(!(G&&!Qt(D,Pi(Z,B,"")))){if(Z)return!1}}}}}}return!0},Gc=function(T){return T!=="annotation-xml"&&th(T,W)},Vc=function(T){We(S.beforeSanitizeAttributes,T,null);const{attributes:z}=T;if(!z||Vs(T))return;const Z={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:it,forceKeepAttr:void 0};let vt=z.length;for(;vt--;){const Ut=z[vt],{name:zt,namespaceURI:se,value:Le}=Ut,Ri=Ot(zt);let Kt=zt==="value"?Le:lx(Le);if(Z.attrName=Ri,Z.attrValue=Kt,Z.keepAttr=!0,Z.forceKeepAttr=void 0,We(S.uponSanitizeAttribute,T,Z),Kt=Z.attrValue,Dc&&(Ri==="id"||Ri==="name")&&(Hn(zt,T),Kt=Dy+Kt),Mt&&Qt(/((--!?|])>)|<\/(style|title)/i,Kt)){Hn(zt,T);continue}if(Z.forceKeepAttr||(Hn(zt,T),!Z.keepAttr))continue;if(!ct&&Qt(/\/>/i,Kt)){Hn(zt,T);continue}P&&Un([I,E,A],Zc=>{Kt=Pi(Kt,Zc," ")});const Xc=Ot(T.nodeName);if(jc(Xc,Ri,Kt)){if(v&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!se)switch(d.getAttributeType(Xc,Ri)){case"TrustedHTML":{Kt=v.createHTML(Kt);break}case"TrustedScriptURL":{Kt=v.createScriptURL(Kt);break}}try{se?T.setAttributeNS(se,zt,Kt):T.setAttribute(zt,Kt),Vs(T)?Ae(T):Jc(t.removed)}catch{}}}We(S.afterSanitizeAttributes,T,null)},Wy=function nt(T){let z=null;const Z=Hc(T);for(We(S.beforeSanitizeShadowDOM,T,null);z=Z.nextNode();)We(S.uponSanitizeShadowNode,z,null),Yc(z),Vc(z),z.content instanceof a&&nt(z.content);We(S.afterSanitizeShadowDOM,T,null)};return t.sanitize=function(nt){let T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},z=null,Z=null,vt=null,Ut=null;if(Ys=!nt,Ys&&(nt="<!-->"),typeof nt!="string"&&!Uc(nt))if(typeof nt.toString=="function"){if(nt=nt.toString(),typeof nt!="string")throw Ni("dirty is not a string, aborting")}else throw Ni("toString is not a function");if(!t.isSupported)return nt;if(Pt||Gs(T),t.removed=[],typeof nt=="string"&&(Di=!1),Di){if(nt.nodeName){const Le=Ot(nt.nodeName);if(!X[Le]||kt[Le])throw Ni("root node is forbidden and cannot be sanitized in-place")}}else if(nt instanceof s)z=qc("<!---->"),Z=z.ownerDocument.importNode(nt,!0),Z.nodeType===Wi.element&&Z.nodeName==="BODY"||Z.nodeName==="HTML"?z=Z:z.appendChild(Z);else{if(!ae&&!P&&!dt&&nt.indexOf("<")===-1)return v&&Pn?v.createHTML(nt):nt;if(z=qc(nt),!z)return ae?null:Pn?k:""}z&&Nt&&Ae(z.firstChild);const zt=Hc(Di?nt:z);for(;vt=zt.nextNode();)Yc(vt),Vc(vt),vt.content instanceof a&&Wy(vt.content);if(Di)return nt;if(ae){if(pr)for(Ut=O.call(z.ownerDocument);z.firstChild;)Ut.appendChild(z.firstChild);else Ut=z;return(it.shadowroot||it.shadowrootmode)&&(Ut=$.call(i,Ut,!0)),Ut}let se=dt?z.outerHTML:z.innerHTML;return dt&&X["!doctype"]&&z.ownerDocument&&z.ownerDocument.doctype&&z.ownerDocument.doctype.name&&Qt(rf,z.ownerDocument.doctype.name)&&(se="<!DOCTYPE "+z.ownerDocument.doctype.name+`>
`+se),P&&Un([I,E,A],Le=>{se=Pi(se,Le," ")}),v&&Pn?v.createHTML(se):se},t.setConfig=function(){let nt=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Gs(nt),Pt=!0},t.clearConfig=function(){Gr=null,Pt=!1},t.isValidAttribute=function(nt,T,z){Gr||Gs({});const Z=Ot(nt),vt=Ot(T);return jc(Z,vt,z)},t.addHook=function(nt,T){typeof T=="function"&&Ii(S[nt],T)},t.removeHook=function(nt,T){if(T!==void 0){const z=ax(S[nt],T);return z===-1?void 0:sx(S[nt],z,1)[0]}return Jc(S[nt])},t.removeHooks=function(nt){S[nt]=[]},t.removeAllHooks=function(){S=sh()},t}var pi=nf(),af=Object.defineProperty,g=(e,t)=>af(e,"name",{value:t,configurable:!0}),kx=(e,t)=>{for(var r in t)af(e,r,{get:t[r],enumerable:!0})},qe={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},R={trace:g((...e)=>{},"trace"),debug:g((...e)=>{},"debug"),info:g((...e)=>{},"info"),warn:g((...e)=>{},"warn"),error:g((...e)=>{},"error"),fatal:g((...e)=>{},"fatal")},Tl=g(function(e="fatal"){let t=qe.fatal;typeof e=="string"?e.toLowerCase()in qe&&(t=qe[e]):typeof e=="number"&&(t=e),R.trace=()=>{},R.debug=()=>{},R.info=()=>{},R.warn=()=>{},R.error=()=>{},R.fatal=()=>{},t<=qe.fatal&&(R.fatal=console.error?console.error.bind(console,pe("FATAL"),"color: orange"):console.log.bind(console,"\x1B[35m",pe("FATAL"))),t<=qe.error&&(R.error=console.error?console.error.bind(console,pe("ERROR"),"color: orange"):console.log.bind(console,"\x1B[31m",pe("ERROR"))),t<=qe.warn&&(R.warn=console.warn?console.warn.bind(console,pe("WARN"),"color: orange"):console.log.bind(console,"\x1B[33m",pe("WARN"))),t<=qe.info&&(R.info=console.info?console.info.bind(console,pe("INFO"),"color: lightblue"):console.log.bind(console,"\x1B[34m",pe("INFO"))),t<=qe.debug&&(R.debug=console.debug?console.debug.bind(console,pe("DEBUG"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",pe("DEBUG"))),t<=qe.trace&&(R.trace=console.debug?console.debug.bind(console,pe("TRACE"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",pe("TRACE")))},"setLogLevel"),pe=g(e=>`%c${Vy().format("ss.SSS")} : ${e} : `,"format"),sf=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,cn=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,vx=/\s*%%.*\n/gm,si,of=(si=class extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}},g(si,"UnknownDiagramError"),si),gi={},Ml=g(function(e,t){e=e.replace(sf,"").replace(cn,"").replace(vx,`
`);for(const[r,{detector:i}]of Object.entries(gi))if(i(e,t))return r;throw new of(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),lf=g((...e)=>{for(const{id:t,detector:r,loader:i}of e)cf(t,r,i)},"registerLazyLoadedDiagrams"),cf=g((e,t,r)=>{gi[e]&&R.warn(`Detector with key ${e} already exists. Overwriting.`),gi[e]={detector:t,loader:r},R.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),Sx=g(e=>gi[e].loader,"getDiagramLoader"),Lo=g((e,t,{depth:r=2,clobber:i=!1}={})=>{const n={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(a=>Lo(e,a,n)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(a=>{e.includes(a)||e.push(a)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(a=>{typeof t[a]=="object"&&(e[a]===void 0||typeof e[a]=="object")?(e[a]===void 0&&(e[a]=Array.isArray(t[a])?[]:{}),e[a]=Lo(e[a],t[a],{depth:r-1,clobber:i})):(i||typeof e[a]!="object"&&typeof t[a]!="object")&&(e[a]=t[a])}),e)},"assignWithDepth"),Ht=Lo,ds="#ffffff",ps="#f2f2f2",Jt=g((e,t)=>t?M(e,{s:-40,l:10}):M(e,{s:-40,l:-10}),"mkBorder"),oi,Tx=(oi=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var r,i,n,a,o,s,c,l,h,u,f,d,p,m,y,x,b,C,v,k,_;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||M(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||M(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||Jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||Jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||Jt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||Jt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||H(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||H(this.tertiaryColor),this.lineColor=this.lineColor||H(this.background),this.arrowheadColor=this.arrowheadColor||H(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?at(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||at(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||H(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||Y(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||at(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||at(this.mainBkg,10)):(this.rowOdd=this.rowOdd||Y(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||Y(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||M(this.primaryColor,{h:30}),this.cScale4=this.cScale4||M(this.primaryColor,{h:60}),this.cScale5=this.cScale5||M(this.primaryColor,{h:90}),this.cScale6=this.cScale6||M(this.primaryColor,{h:120}),this.cScale7=this.cScale7||M(this.primaryColor,{h:150}),this.cScale8=this.cScale8||M(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||M(this.primaryColor,{h:270}),this.cScale10=this.cScale10||M(this.primaryColor,{h:300}),this.cScale11=this.cScale11||M(this.primaryColor,{h:330}),this.darkMode)for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScale"+w]=at(this["cScale"+w],75);else for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScale"+w]=at(this["cScale"+w],25);for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScaleInv"+w]=this["cScaleInv"+w]||H(this["cScale"+w]);for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this.darkMode?this["cScalePeer"+w]=this["cScalePeer"+w]||Y(this["cScale"+w],10):this["cScalePeer"+w]=this["cScalePeer"+w]||at(this["cScale"+w],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let w=0;w<this.THEME_COLOR_LIMIT;w++)this["cScaleLabel"+w]=this["cScaleLabel"+w]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let w=0;w<5;w++)this["surface"+w]=this["surface"+w]||M(this.mainBkg,{h:180,s:-15,l:t*(5+w*3)}),this["surfacePeer"+w]=this["surfacePeer"+w]||M(this.mainBkg,{h:180,s:-15,l:t*(8+w*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||M(this.primaryColor,{h:64}),this.fillType3=this.fillType3||M(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||M(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||M(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||M(this.primaryColor,{h:128}),this.fillType7=this.fillType7||M(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||M(this.primaryColor,{l:-10}),this.pie5=this.pie5||M(this.secondaryColor,{l:-10}),this.pie6=this.pie6||M(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||M(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||M(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||M(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||M(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||M(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||M(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.radar={axisColor:((r=this.radar)==null?void 0:r.axisColor)||this.lineColor,axisStrokeWidth:((i=this.radar)==null?void 0:i.axisStrokeWidth)||2,axisLabelFontSize:((n=this.radar)==null?void 0:n.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((o=this.radar)==null?void 0:o.curveStrokeWidth)||2,graticuleColor:((s=this.radar)==null?void 0:s.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((c=this.radar)==null?void 0:c.graticuleStrokeWidth)||1,graticuleOpacity:((l=this.radar)==null?void 0:l.graticuleOpacity)||.3,legendBoxSize:((h=this.radar)==null?void 0:h.legendBoxSize)||12,legendFontSize:((u=this.radar)==null?void 0:u.legendFontSize)||12},this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||M(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||M(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||M(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||M(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||M(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||M(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mn(this.quadrant1Fill)?Y(this.quadrant1Fill):at(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((f=this.xyChart)==null?void 0:f.backgroundColor)||this.background,titleColor:((d=this.xyChart)==null?void 0:d.titleColor)||this.primaryTextColor,xAxisTitleColor:((p=this.xyChart)==null?void 0:p.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((m=this.xyChart)==null?void 0:m.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((y=this.xyChart)==null?void 0:y.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((x=this.xyChart)==null?void 0:x.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((b=this.xyChart)==null?void 0:b.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((C=this.xyChart)==null?void 0:C.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((v=this.xyChart)==null?void 0:v.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((k=this.xyChart)==null?void 0:k.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((_=this.xyChart)==null?void 0:_.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?at(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||M(this.primaryColor,{h:-30}),this.git4=this.git4||M(this.primaryColor,{h:-60}),this.git5=this.git5||M(this.primaryColor,{h:-90}),this.git6=this.git6||M(this.primaryColor,{h:60}),this.git7=this.git7||M(this.primaryColor,{h:120}),this.darkMode?(this.git0=Y(this.git0,25),this.git1=Y(this.git1,25),this.git2=Y(this.git2,25),this.git3=Y(this.git3,25),this.git4=Y(this.git4,25),this.git5=Y(this.git5,25),this.git6=Y(this.git6,25),this.git7=Y(this.git7,25)):(this.git0=at(this.git0,25),this.git1=at(this.git1,25),this.git2=at(this.git2,25),this.git3=at(this.git3,25),this.git4=at(this.git4,25),this.git5=at(this.git5,25),this.git6=at(this.git6,25),this.git7=at(this.git7,25)),this.gitInv0=this.gitInv0||H(this.git0),this.gitInv1=this.gitInv1||H(this.git1),this.gitInv2=this.gitInv2||H(this.git2),this.gitInv3=this.gitInv3||H(this.git3),this.gitInv4=this.gitInv4||H(this.git4),this.gitInv5=this.gitInv5||H(this.git5),this.gitInv6=this.gitInv6||H(this.git6),this.gitInv7=this.gitInv7||H(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||ds,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||ps}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},g(oi,"Theme"),oi),Mx=g(e=>{const t=new Tx;return t.calculate(e),t},"getThemeVariables"),li,Ax=(li=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=Y(this.primaryColor,16),this.tertiaryColor=M(this.primaryColor,{h:-160}),this.primaryBorderColor=H(this.background),this.secondaryBorderColor=Jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=H(this.primaryColor),this.secondaryTextColor=H(this.secondaryColor),this.tertiaryTextColor=H(this.tertiaryColor),this.lineColor=H(this.background),this.textColor=H(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=Y(H("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=ln(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=at("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=at(this.sectionBkgColor,10),this.taskBorderColor=ln(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=ln(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||Y(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||at(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,p,m,y,x,b,C,v,k;this.secondBkg=Y(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=Y(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=Y(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=M(this.primaryColor,{h:64}),this.fillType3=M(this.secondaryColor,{h:64}),this.fillType4=M(this.primaryColor,{h:-64}),this.fillType5=M(this.secondaryColor,{h:-64}),this.fillType6=M(this.primaryColor,{h:128}),this.fillType7=M(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||M(this.primaryColor,{h:30}),this.cScale4=this.cScale4||M(this.primaryColor,{h:60}),this.cScale5=this.cScale5||M(this.primaryColor,{h:90}),this.cScale6=this.cScale6||M(this.primaryColor,{h:120}),this.cScale7=this.cScale7||M(this.primaryColor,{h:150}),this.cScale8=this.cScale8||M(this.primaryColor,{h:210}),this.cScale9=this.cScale9||M(this.primaryColor,{h:270}),this.cScale10=this.cScale10||M(this.primaryColor,{h:300}),this.cScale11=this.cScale11||M(this.primaryColor,{h:330});for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||H(this["cScale"+_]);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScalePeer"+_]=this["cScalePeer"+_]||Y(this["cScale"+_],10);for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||M(this.mainBkg,{h:30,s:-30,l:-(-10+_*4)}),this["surfacePeer"+_]=this["surfacePeer"+_]||M(this.mainBkg,{h:30,s:-30,l:-(-7+_*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["pie"+_]=this["cScale"+_];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||M(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||M(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||M(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||M(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||M(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||M(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mn(this.quadrant1Fill)?Y(this.quadrant1Fill):at(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((n=this.xyChart)==null?void 0:n.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((a=this.xyChart)==null?void 0:a.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.radar={axisColor:((f=this.radar)==null?void 0:f.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((p=this.radar)==null?void 0:p.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((C=this.radar)==null?void 0:C.graticuleOpacity)||.3,legendBoxSize:((v=this.radar)==null?void 0:v.legendBoxSize)||12,legendFontSize:((k=this.radar)==null?void 0:k.legendFontSize)||12},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?at(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=Y(this.secondaryColor,20),this.git1=Y(this.pie2||this.secondaryColor,20),this.git2=Y(this.pie3||this.tertiaryColor,20),this.git3=Y(this.pie4||M(this.primaryColor,{h:-30}),20),this.git4=Y(this.pie5||M(this.primaryColor,{h:-60}),20),this.git5=Y(this.pie6||M(this.primaryColor,{h:-90}),10),this.git6=Y(this.pie7||M(this.primaryColor,{h:60}),10),this.git7=Y(this.pie8||M(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||H(this.git0),this.gitInv1=this.gitInv1||H(this.git1),this.gitInv2=this.gitInv2||H(this.git2),this.gitInv3=this.gitInv3||H(this.git3),this.gitInv4=this.gitInv4||H(this.git4),this.gitInv5=this.gitInv5||H(this.git5),this.gitInv6=this.gitInv6||H(this.git6),this.gitInv7=this.gitInv7||H(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||H(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||H(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Y(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Y(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},g(li,"Theme"),li),Lx=g(e=>{const t=new Ax;return t.calculate(e),t},"getThemeVariables"),ci,Bx=(ci=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=M(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=M(this.primaryColor,{h:-160}),this.primaryBorderColor=Jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=H(this.primaryColor),this.secondaryTextColor=H(this.secondaryColor),this.tertiaryTextColor=H(this.tertiaryColor),this.lineColor=H(this.background),this.textColor=H(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=ln(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,p,m,y,x,b,C,v,k;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||M(this.primaryColor,{h:30}),this.cScale4=this.cScale4||M(this.primaryColor,{h:60}),this.cScale5=this.cScale5||M(this.primaryColor,{h:90}),this.cScale6=this.cScale6||M(this.primaryColor,{h:120}),this.cScale7=this.cScale7||M(this.primaryColor,{h:150}),this.cScale8=this.cScale8||M(this.primaryColor,{h:210}),this.cScale9=this.cScale9||M(this.primaryColor,{h:270}),this.cScale10=this.cScale10||M(this.primaryColor,{h:300}),this.cScale11=this.cScale11||M(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||at(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||at(this.tertiaryColor,40);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScale"+_]=at(this["cScale"+_],10),this["cScalePeer"+_]=this["cScalePeer"+_]||at(this["cScale"+_],25);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||M(this["cScale"+_],{h:180});for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||M(this.mainBkg,{h:30,l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||M(this.mainBkg,{h:30,l:-(7+_*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||H(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||H(this.labelTextColor);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=Y(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||Y(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||Y(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=M(this.primaryColor,{h:64}),this.fillType3=M(this.secondaryColor,{h:64}),this.fillType4=M(this.primaryColor,{h:-64}),this.fillType5=M(this.secondaryColor,{h:-64}),this.fillType6=M(this.primaryColor,{h:128}),this.fillType7=M(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||M(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||M(this.primaryColor,{l:-10}),this.pie5=this.pie5||M(this.secondaryColor,{l:-30}),this.pie6=this.pie6||M(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||M(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||M(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||M(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||M(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||M(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||M(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||M(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||M(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||M(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||M(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||M(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||M(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mn(this.quadrant1Fill)?Y(this.quadrant1Fill):at(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((a=this.radar)==null?void 0:a.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((f=this.xyChart)==null?void 0:f.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((p=this.xyChart)==null?void 0:p.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((C=this.xyChart)==null?void 0:C.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((v=this.xyChart)==null?void 0:v.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((k=this.xyChart)==null?void 0:k.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||M(this.primaryColor,{h:-30}),this.git4=this.git4||M(this.primaryColor,{h:-60}),this.git5=this.git5||M(this.primaryColor,{h:-90}),this.git6=this.git6||M(this.primaryColor,{h:60}),this.git7=this.git7||M(this.primaryColor,{h:120}),this.darkMode?(this.git0=Y(this.git0,25),this.git1=Y(this.git1,25),this.git2=Y(this.git2,25),this.git3=Y(this.git3,25),this.git4=Y(this.git4,25),this.git5=Y(this.git5,25),this.git6=Y(this.git6,25),this.git7=Y(this.git7,25)):(this.git0=at(this.git0,25),this.git1=at(this.git1,25),this.git2=at(this.git2,25),this.git3=at(this.git3,25),this.git4=at(this.git4,25),this.git5=at(this.git5,25),this.git6=at(this.git6,25),this.git7=at(this.git7,25)),this.gitInv0=this.gitInv0||at(H(this.git0),25),this.gitInv1=this.gitInv1||H(this.git1),this.gitInv2=this.gitInv2||H(this.git2),this.gitInv3=this.gitInv3||H(this.git3),this.gitInv4=this.gitInv4||H(this.git4),this.gitInv5=this.gitInv5||H(this.git5),this.gitInv6=this.gitInv6||H(this.git6),this.gitInv7=this.gitInv7||H(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||H(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||H(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||ds,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||ps}calculate(t){if(Object.keys(this).forEach(i=>{this[i]==="calculated"&&(this[i]=void 0)}),typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},g(ci,"Theme"),ci),Ex=g(e=>{const t=new Bx;return t.calculate(e),t},"getThemeVariables"),hi,Fx=(hi=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=Y("#cde498",10),this.primaryBorderColor=Jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=H(this.primaryColor),this.secondaryTextColor=H(this.secondaryColor),this.tertiaryTextColor=H(this.primaryColor),this.lineColor=H(this.background),this.textColor=H(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,p,m,y,x,b,C,v,k;this.actorBorder=at(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||M(this.primaryColor,{h:30}),this.cScale4=this.cScale4||M(this.primaryColor,{h:60}),this.cScale5=this.cScale5||M(this.primaryColor,{h:90}),this.cScale6=this.cScale6||M(this.primaryColor,{h:120}),this.cScale7=this.cScale7||M(this.primaryColor,{h:150}),this.cScale8=this.cScale8||M(this.primaryColor,{h:210}),this.cScale9=this.cScale9||M(this.primaryColor,{h:270}),this.cScale10=this.cScale10||M(this.primaryColor,{h:300}),this.cScale11=this.cScale11||M(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||at(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||at(this.tertiaryColor,40);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScale"+_]=at(this["cScale"+_],10),this["cScalePeer"+_]=this["cScalePeer"+_]||at(this["cScale"+_],25);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||M(this["cScale"+_],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||M(this.mainBkg,{h:30,s:-30,l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||M(this.mainBkg,{h:30,s:-30,l:-(8+_*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||Y(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||Y(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=M(this.primaryColor,{h:64}),this.fillType3=M(this.secondaryColor,{h:64}),this.fillType4=M(this.primaryColor,{h:-64}),this.fillType5=M(this.secondaryColor,{h:-64}),this.fillType6=M(this.primaryColor,{h:128}),this.fillType7=M(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||M(this.primaryColor,{l:-30}),this.pie5=this.pie5||M(this.secondaryColor,{l:-30}),this.pie6=this.pie6||M(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||M(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||M(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||M(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||M(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||M(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||M(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||M(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||M(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||M(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||M(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||M(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||M(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mn(this.quadrant1Fill)?Y(this.quadrant1Fill):at(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((a=this.radar)==null?void 0:a.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((f=this.xyChart)==null?void 0:f.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((p=this.xyChart)==null?void 0:p.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((C=this.xyChart)==null?void 0:C.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((v=this.xyChart)==null?void 0:v.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((k=this.xyChart)==null?void 0:k.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||M(this.primaryColor,{h:-30}),this.git4=this.git4||M(this.primaryColor,{h:-60}),this.git5=this.git5||M(this.primaryColor,{h:-90}),this.git6=this.git6||M(this.primaryColor,{h:60}),this.git7=this.git7||M(this.primaryColor,{h:120}),this.darkMode?(this.git0=Y(this.git0,25),this.git1=Y(this.git1,25),this.git2=Y(this.git2,25),this.git3=Y(this.git3,25),this.git4=Y(this.git4,25),this.git5=Y(this.git5,25),this.git6=Y(this.git6,25),this.git7=Y(this.git7,25)):(this.git0=at(this.git0,25),this.git1=at(this.git1,25),this.git2=at(this.git2,25),this.git3=at(this.git3,25),this.git4=at(this.git4,25),this.git5=at(this.git5,25),this.git6=at(this.git6,25),this.git7=at(this.git7,25)),this.gitInv0=this.gitInv0||H(this.git0),this.gitInv1=this.gitInv1||H(this.git1),this.gitInv2=this.gitInv2||H(this.git2),this.gitInv3=this.gitInv3||H(this.git3),this.gitInv4=this.gitInv4||H(this.git4),this.gitInv5=this.gitInv5||H(this.git5),this.gitInv6=this.gitInv6||H(this.git6),this.gitInv7=this.gitInv7||H(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||H(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||H(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||ds,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||ps}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},g(hi,"Theme"),hi),$x=g(e=>{const t=new Fx;return t.calculate(e),t},"getThemeVariables"),ui,Dx=(ui=class{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=Y(this.contrast,55),this.background="#ffffff",this.tertiaryColor=M(this.primaryColor,{h:-160}),this.primaryBorderColor=Jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=H(this.primaryColor),this.secondaryTextColor=H(this.secondaryColor),this.tertiaryTextColor=H(this.tertiaryColor),this.lineColor=H(this.background),this.textColor=H(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||Y(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,p,m,y,x,b,C,v,k;this.secondBkg=Y(this.contrast,55),this.border2=this.contrast,this.actorBorder=Y(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||H(this["cScale"+_]);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this.darkMode?this["cScalePeer"+_]=this["cScalePeer"+_]||Y(this["cScale"+_],10):this["cScalePeer"+_]=this["cScalePeer"+_]||at(this["cScale"+_],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||M(this.mainBkg,{l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||M(this.mainBkg,{l:-(8+_*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=Y(this.contrast,30),this.sectionBkgColor2=Y(this.contrast,30),this.taskBorderColor=at(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=Y(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=at(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=M(this.primaryColor,{h:64}),this.fillType3=M(this.secondaryColor,{h:64}),this.fillType4=M(this.primaryColor,{h:-64}),this.fillType5=M(this.secondaryColor,{h:-64}),this.fillType6=M(this.primaryColor,{h:128}),this.fillType7=M(this.secondaryColor,{h:128});for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["pie"+_]=this["cScale"+_];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||M(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||M(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||M(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||M(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||M(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||M(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mn(this.quadrant1Fill)?Y(this.quadrant1Fill):at(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((n=this.xyChart)==null?void 0:n.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((a=this.xyChart)==null?void 0:a.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.radar={axisColor:((f=this.radar)==null?void 0:f.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((p=this.radar)==null?void 0:p.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((C=this.radar)==null?void 0:C.graticuleOpacity)||.3,legendBoxSize:((v=this.radar)==null?void 0:v.legendBoxSize)||12,legendFontSize:((k=this.radar)==null?void 0:k.legendFontSize)||12},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=at(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||M(this.primaryColor,{h:-30}),this.git4=this.pie5||M(this.primaryColor,{h:-60}),this.git5=this.pie6||M(this.primaryColor,{h:-90}),this.git6=this.pie7||M(this.primaryColor,{h:60}),this.git7=this.pie8||M(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||H(this.git0),this.gitInv1=this.gitInv1||H(this.git1),this.gitInv2=this.gitInv2||H(this.git2),this.gitInv3=this.gitInv3||H(this.git3),this.gitInv4=this.gitInv4||H(this.git4),this.gitInv5=this.gitInv5||H(this.git5),this.gitInv6=this.gitInv6||H(this.git6),this.gitInv7=this.gitInv7||H(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||ds,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||ps}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},g(ui,"Theme"),ui),Ox=g(e=>{const t=new Dx;return t.calculate(e),t},"getThemeVariables"),Ze={base:{getThemeVariables:Mx},dark:{getThemeVariables:Lx},default:{getThemeVariables:Ex},forest:{getThemeVariables:$x},neutral:{getThemeVariables:Ox}},He={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},radar:{useMaxWidth:!0,width:600,height:600,marginTop:50,marginRight:50,marginBottom:50,marginLeft:50,axisScaleFactor:1,axisLabelFactor:1.05,curveTension:.17},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},hf={...He,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:Ze.default.getThemeVariables(),sequence:{...He.sequence,messageFont:g(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:g(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:g(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{...He.gantt,tickInterval:void 0,useWidth:void 0},c4:{...He.c4,useWidth:void 0,personFont:g(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),external_personFont:g(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:g(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:g(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:g(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:g(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:g(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:g(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:g(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:g(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:g(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:g(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:g(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:g(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:g(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:g(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:g(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:g(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:g(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:g(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:g(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:g(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{...He.pie,useWidth:984},xyChart:{...He.xyChart,useWidth:void 0},requirement:{...He.requirement,useWidth:void 0},packet:{...He.packet},radar:{...He.radar}},uf=g((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...uf(e[i],"")]:[...r,t+i],[]),"keyify"),Rx=new Set(uf(hf,"")),ff=hf,wa=g(e=>{if(R.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>wa(t));return}for(const t of Object.keys(e)){if(R.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!Rx.has(t)||e[t]==null){R.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){R.debug("sanitizing object",t),wa(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&(R.debug("sanitizing css option",t),e[t]=Ix(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r!=null&&r.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}R.debug("After sanitization",e)}},"sanitizeDirective"),Ix=g(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),mi=Object.freeze(ff),le=Ht({},mi),df,yi=[],hn=Ht({},mi),gs=g((e,t)=>{let r=Ht({},e),i={};for(const n of t)mf(n),i=Ht(i,n);if(r=Ht(r,i),i.theme&&i.theme in Ze){const n=Ht({},df),a=Ht(n.themeVariables||{},i.themeVariables);r.theme&&r.theme in Ze&&(r.themeVariables=Ze[r.theme].getThemeVariables(a))}return hn=r,yf(hn),hn},"updateCurrentConfig"),Px=g(e=>(le=Ht({},mi),le=Ht(le,e),e.theme&&Ze[e.theme]&&(le.themeVariables=Ze[e.theme].getThemeVariables(e.themeVariables)),gs(le,yi),le),"setSiteConfig"),Nx=g(e=>{df=Ht({},e)},"saveConfigFromInitialize"),zx=g(e=>(le=Ht(le,e),gs(le,yi),le),"updateSiteConfig"),pf=g(()=>Ht({},le),"getSiteConfig"),gf=g(e=>(yf(e),Ht(hn,e),he()),"setConfig"),he=g(()=>Ht({},hn),"getConfig"),mf=g(e=>{e&&(["secure",...le.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&(R.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&mf(e[t])}))},"sanitize"),Wx=g(e=>{var t;wa(e),e.fontFamily&&!((t=e.themeVariables)!=null&&t.fontFamily)&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),yi.push(e),gs(le,yi)},"addDirective"),ka=g((e=le)=>{yi=[],gs(e,yi)},"reset"),qx={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},oh={},Hx=g(e=>{oh[e]||(R.warn(qx[e]),oh[e]=!0)},"issueWarning"),yf=g(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&Hx("LAZY_LOAD_DEPRECATED")},"checkConfig"),An=/<br\s*\/?>/gi,Ux=g(e=>e?_f(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),Yx=(()=>{let e=!1;return()=>{e||(xf(),e=!0)}})();function xf(){const e="data-temp-href-target";pi.addHook("beforeSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),pi.addHook("afterSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}g(xf,"setupDompurifyHooks");var bf=g(e=>(Yx(),pi.sanitize(e)),"removeScript"),lh=g((e,t)=>{var r;if(((r=t.flowchart)==null?void 0:r.htmlLabels)!==!1){const i=t.securityLevel;i==="antiscript"||i==="strict"?e=bf(e):i!=="loose"&&(e=_f(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=Xx(e))}return e},"sanitizeMore"),Ar=g((e,t)=>e&&(t.dompurifyConfig?e=pi.sanitize(lh(e,t),t.dompurifyConfig).toString():e=pi.sanitize(lh(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),jx=g((e,t)=>typeof e=="string"?Ar(e,t):e.flat().map(r=>Ar(r,t)),"sanitizeTextOrArray"),Gx=g(e=>An.test(e),"hasBreaks"),Vx=g(e=>e.split(An),"splitBreaks"),Xx=g(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),_f=g(e=>e.replace(An,"#br#"),"breakToPlaceholder"),Zx=g(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=t.replaceAll(/\(/g,"\\("),t=t.replaceAll(/\)/g,"\\)")),t},"getUrl"),$t=g(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),Kx=g(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),Qx=g(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),ch=g(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let n=t[i];if(n===","&&i>0&&i+1<t.length){const a=t[i-1],o=t[i+1];Jx(a,o)&&(n=a+","+o,i++,r.pop())}r.push(tb(n))}return r.join("")},"parseGenericTypes"),Bo=g((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),Jx=g((e,t)=>{const r=Bo(e,"~"),i=Bo(t,"~");return r===1&&i===1},"shouldCombineSets"),tb=g(e=>{const t=Bo(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let n=i.indexOf("~"),a=i.lastIndexOf("~");for(;n!==-1&&a!==-1&&n!==a;)i[n]="<",i[a]=">",n=i.indexOf("~"),a=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),hh=g(()=>window.MathMLElement!==void 0,"isMathMLSupported"),Eo=/\$\$(.*)\$\$/g,xi=g(e=>{var t;return(((t=e.match(Eo))==null?void 0:t.length)??0)>0},"hasKatex"),s3=g(async(e,t)=>{e=await Al(e,t);const r=document.createElement("div");r.innerHTML=e,r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0";const i=document.querySelector("body");i==null||i.insertAdjacentElement("beforeend",r);const n={width:r.clientWidth,height:r.clientHeight};return r.remove(),n},"calculateMathMLDimensions"),Al=g(async(e,t)=>{if(!xi(e))return e;if(!(hh()||t.legacyMathML||t.forceLegacyMathML))return e.replace(Eo,"MathML is unsupported in this environment.");const{default:r}=await _t(async()=>{const{default:n}=await import("./katex-DCmpTppl.js");return{default:n}},[]),i=t.forceLegacyMathML||!hh()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(An).map(n=>xi(n)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${n}</div>`:`<div>${n}</div>`).join("").replace(Eo,(n,a)=>r.renderToString(a,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))},"renderKatex"),Ai={getRows:Ux,sanitizeText:Ar,sanitizeTextOrArray:jx,hasBreaks:Gx,splitBreaks:Vx,lineBreakRegex:An,removeScript:bf,getUrl:Zx,evaluate:$t,getMax:Kx,getMin:Qx},eb=g(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),rb=g(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),Cf=g(function(e,t,r,i){const n=rb(t,r,i);eb(e,n)},"configureSvgSize"),ib=g(function(e,t,r,i){const n=t.node().getBBox(),a=n.width,o=n.height;R.info(`SVG bounds: ${a}x${o}`,n);let s=0,c=0;R.info(`Graph bounds: ${s}x${c}`,e),s=a+r*2,c=o+r*2,R.info(`Calculated bounds: ${s}x${c}`),Cf(t,c,s,i);const l=`${n.x-r} ${n.y-r} ${n.width+2*r} ${n.height+2*r}`;t.attr("viewBox",l)},"setupGraphViewbox"),oa={},nb=g((e,t,r)=>{let i="";return e in oa&&oa[e]?i=oa[e](r):R.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),ab=g((e,t)=>{t!==void 0&&(oa[e]=t)},"addStylesForDiagram"),sb=nb,wf={};kx(wf,{clear:()=>ob,getAccDescription:()=>ub,getAccTitle:()=>cb,getDiagramTitle:()=>db,setAccDescription:()=>hb,setAccTitle:()=>lb,setDiagramTitle:()=>fb});var Ll="",Bl="",El="",Fl=g(e=>Ar(e,he()),"sanitizeText"),ob=g(()=>{Ll="",El="",Bl=""},"clear"),lb=g(e=>{Ll=Fl(e).replace(/^\s+/g,"")},"setAccTitle"),cb=g(()=>Ll,"getAccTitle"),hb=g(e=>{El=Fl(e).replace(/\n\s+/g,`
`)},"setAccDescription"),ub=g(()=>El,"getAccDescription"),fb=g(e=>{Bl=Fl(e)},"setDiagramTitle"),db=g(()=>Bl,"getDiagramTitle"),uh=R,pb=Tl,yt=he,o3=gf,l3=mi,$l=g(e=>Ar(e,yt()),"sanitizeText"),gb=ib,mb=g(()=>wf,"getCommonDb"),va={},Sa=g((e,t,r)=>{var i;va[e]&&uh.warn(`Diagram with id ${e} already registered. Overwriting.`),va[e]=t,r&&cf(e,r),ab(e,t.styles),(i=t.injectUtils)==null||i.call(t,uh,pb,yt,$l,gb,mb(),()=>{})},"registerDiagram"),Fo=g(e=>{if(e in va)return va[e];throw new yb(e)},"getDiagram"),fi,yb=(fi=class extends Error{constructor(t){super(`Diagram ${t} not found.`)}},g(fi,"DiagramNotFoundError"),fi);function Dl(e){return typeof e>"u"||e===null}g(Dl,"isNothing");function kf(e){return typeof e=="object"&&e!==null}g(kf,"isObject");function vf(e){return Array.isArray(e)?e:Dl(e)?[]:[e]}g(vf,"toArray");function Sf(e,t){var r,i,n,a;if(t)for(a=Object.keys(t),r=0,i=a.length;r<i;r+=1)n=a[r],e[n]=t[n];return e}g(Sf,"extend");function Tf(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}g(Tf,"repeat");function Mf(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}g(Mf,"isNegativeZero");var xb=Dl,bb=kf,_b=vf,Cb=Tf,wb=Mf,kb=Sf,Ft={isNothing:xb,isObject:bb,toArray:_b,repeat:Cb,isNegativeZero:wb,extend:kb};function Ol(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}g(Ol,"formatError");function bi(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=Ol(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}g(bi,"YAMLException$1");bi.prototype=Object.create(Error.prototype);bi.prototype.constructor=bi;bi.prototype.toString=g(function(t){return this.name+": "+Ol(this,t)},"toString");var ce=bi;function la(e,t,r,i,n){var a="",o="",s=Math.floor(n/2)-1;return i-t>s&&(a=" ... ",t=i-s+a.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:a+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+a.length}}g(la,"getLine");function ca(e,t){return Ft.repeat(" ",t-e.length)+e}g(ca,"padStart");function Af(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],n=[],a,o=-1;a=r.exec(e.buffer);)n.push(a.index),i.push(a.index+a[0].length),e.position<=a.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",c,l,h=Math.min(e.line+t.linesAfter,n.length).toString().length,u=t.maxLength-(t.indent+h+3);for(c=1;c<=t.linesBefore&&!(o-c<0);c++)l=la(e.buffer,i[o-c],n[o-c],e.position-(i[o]-i[o-c]),u),s=Ft.repeat(" ",t.indent)+ca((e.line-c+1).toString(),h)+" | "+l.str+`
`+s;for(l=la(e.buffer,i[o],n[o],e.position,u),s+=Ft.repeat(" ",t.indent)+ca((e.line+1).toString(),h)+" | "+l.str+`
`,s+=Ft.repeat("-",t.indent+h+3+l.pos)+`^
`,c=1;c<=t.linesAfter&&!(o+c>=n.length);c++)l=la(e.buffer,i[o+c],n[o+c],e.position-(i[o]-i[o+c]),u),s+=Ft.repeat(" ",t.indent)+ca((e.line+c+1).toString(),h)+" | "+l.str+`
`;return s.replace(/\n$/,"")}g(Af,"makeSnippet");var vb=Af,Sb=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],Tb=["scalar","sequence","mapping"];function Lf(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}g(Lf,"compileStyleAliases");function Bf(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(Sb.indexOf(r)===-1)throw new ce('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=Lf(t.styleAliases||null),Tb.indexOf(this.kind)===-1)throw new ce('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}g(Bf,"Type$1");var Xt=Bf;function $o(e,t){var r=[];return e[t].forEach(function(i){var n=r.length;r.forEach(function(a,o){a.tag===i.tag&&a.kind===i.kind&&a.multi===i.multi&&(n=o)}),r[n]=i}),r}g($o,"compileList");function Ef(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(n){n.multi?(e.multi[n.kind].push(n),e.multi.fallback.push(n)):e[n.kind][n.tag]=e.fallback[n.tag]=n}for(g(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}g(Ef,"compileMap");function Ta(e){return this.extend(e)}g(Ta,"Schema$1");Ta.prototype.extend=g(function(t){var r=[],i=[];if(t instanceof Xt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new ce("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(a){if(!(a instanceof Xt))throw new ce("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(a.loadKind&&a.loadKind!=="scalar")throw new ce("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(a.multi)throw new ce("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(a){if(!(a instanceof Xt))throw new ce("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var n=Object.create(Ta.prototype);return n.implicit=(this.implicit||[]).concat(r),n.explicit=(this.explicit||[]).concat(i),n.compiledImplicit=$o(n,"implicit"),n.compiledExplicit=$o(n,"explicit"),n.compiledTypeMap=Ef(n.compiledImplicit,n.compiledExplicit),n},"extend");var Mb=Ta,Ab=new Xt("tag:yaml.org,2002:str",{kind:"scalar",construct:g(function(e){return e!==null?e:""},"construct")}),Lb=new Xt("tag:yaml.org,2002:seq",{kind:"sequence",construct:g(function(e){return e!==null?e:[]},"construct")}),Bb=new Xt("tag:yaml.org,2002:map",{kind:"mapping",construct:g(function(e){return e!==null?e:{}},"construct")}),Eb=new Mb({explicit:[Ab,Lb,Bb]});function Ff(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}g(Ff,"resolveYamlNull");function $f(){return null}g($f,"constructYamlNull");function Df(e){return e===null}g(Df,"isNull");var Fb=new Xt("tag:yaml.org,2002:null",{kind:"scalar",resolve:Ff,construct:$f,predicate:Df,represent:{canonical:g(function(){return"~"},"canonical"),lowercase:g(function(){return"null"},"lowercase"),uppercase:g(function(){return"NULL"},"uppercase"),camelcase:g(function(){return"Null"},"camelcase"),empty:g(function(){return""},"empty")},defaultStyle:"lowercase"});function Of(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}g(Of,"resolveYamlBoolean");function Rf(e){return e==="true"||e==="True"||e==="TRUE"}g(Rf,"constructYamlBoolean");function If(e){return Object.prototype.toString.call(e)==="[object Boolean]"}g(If,"isBoolean");var $b=new Xt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Of,construct:Rf,predicate:If,represent:{lowercase:g(function(e){return e?"true":"false"},"lowercase"),uppercase:g(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:g(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function Pf(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}g(Pf,"isHexCode");function Nf(e){return 48<=e&&e<=55}g(Nf,"isOctCode");function zf(e){return 48<=e&&e<=57}g(zf,"isDecCode");function Wf(e){if(e===null)return!1;var t=e.length,r=0,i=!1,n;if(!t)return!1;if(n=e[r],(n==="-"||n==="+")&&(n=e[++r]),n==="0"){if(r+1===t)return!0;if(n=e[++r],n==="b"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(n!=="0"&&n!=="1")return!1;i=!0}return i&&n!=="_"}if(n==="x"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(!Pf(e.charCodeAt(r)))return!1;i=!0}return i&&n!=="_"}if(n==="o"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(!Nf(e.charCodeAt(r)))return!1;i=!0}return i&&n!=="_"}}if(n==="_")return!1;for(;r<t;r++)if(n=e[r],n!=="_"){if(!zf(e.charCodeAt(r)))return!1;i=!0}return!(!i||n==="_")}g(Wf,"resolveYamlInteger");function qf(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}g(qf,"constructYamlInteger");function Hf(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!Ft.isNegativeZero(e)}g(Hf,"isInteger");var Db=new Xt("tag:yaml.org,2002:int",{kind:"scalar",resolve:Wf,construct:qf,predicate:Hf,represent:{binary:g(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:g(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:g(function(e){return e.toString(10)},"decimal"),hexadecimal:g(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Ob=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Uf(e){return!(e===null||!Ob.test(e)||e[e.length-1]==="_")}g(Uf,"resolveYamlFloat");function Yf(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}g(Yf,"constructYamlFloat");var Rb=/^[-+]?[0-9]+e/;function jf(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Ft.isNegativeZero(e))return"-0.0";return r=e.toString(10),Rb.test(r)?r.replace("e",".e"):r}g(jf,"representYamlFloat");function Gf(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||Ft.isNegativeZero(e))}g(Gf,"isFloat");var Ib=new Xt("tag:yaml.org,2002:float",{kind:"scalar",resolve:Uf,construct:Yf,predicate:Gf,represent:jf,defaultStyle:"lowercase"}),Vf=Eb.extend({implicit:[Fb,$b,Db,Ib]}),Pb=Vf,Xf=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),Zf=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Kf(e){return e===null?!1:Xf.exec(e)!==null||Zf.exec(e)!==null}g(Kf,"resolveYamlTimestamp");function Qf(e){var t,r,i,n,a,o,s,c=0,l=null,h,u,f;if(t=Xf.exec(e),t===null&&(t=Zf.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,n=+t[3],!t[4])return new Date(Date.UTC(r,i,n));if(a=+t[4],o=+t[5],s=+t[6],t[7]){for(c=t[7].slice(0,3);c.length<3;)c+="0";c=+c}return t[9]&&(h=+t[10],u=+(t[11]||0),l=(h*60+u)*6e4,t[9]==="-"&&(l=-l)),f=new Date(Date.UTC(r,i,n,a,o,s,c)),l&&f.setTime(f.getTime()-l),f}g(Qf,"constructYamlTimestamp");function Jf(e){return e.toISOString()}g(Jf,"representYamlTimestamp");var Nb=new Xt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Kf,construct:Qf,instanceOf:Date,represent:Jf});function td(e){return e==="<<"||e===null}g(td,"resolveYamlMerge");var zb=new Xt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:td}),Rl=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function ed(e){if(e===null)return!1;var t,r,i=0,n=e.length,a=Rl;for(r=0;r<n;r++)if(t=a.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}g(ed,"resolveYamlBinary");function rd(e){var t,r,i=e.replace(/[\r\n=]/g,""),n=i.length,a=Rl,o=0,s=[];for(t=0;t<n;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|a.indexOf(i.charAt(t));return r=n%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}g(rd,"constructYamlBinary");function id(e){var t="",r=0,i,n,a=e.length,o=Rl;for(i=0;i<a;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return n=a%3,n===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):n===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):n===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}g(id,"representYamlBinary");function nd(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}g(nd,"isBinary");var Wb=new Xt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:ed,construct:rd,predicate:nd,represent:id}),qb=Object.prototype.hasOwnProperty,Hb=Object.prototype.toString;function ad(e){if(e===null)return!0;var t=[],r,i,n,a,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(n=s[r],o=!1,Hb.call(n)!=="[object Object]")return!1;for(a in n)if(qb.call(n,a))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(a)===-1)t.push(a);else return!1}return!0}g(ad,"resolveYamlOmap");function sd(e){return e!==null?e:[]}g(sd,"constructYamlOmap");var Ub=new Xt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:ad,construct:sd}),Yb=Object.prototype.toString;function od(e){if(e===null)return!0;var t,r,i,n,a,o=e;for(a=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],Yb.call(i)!=="[object Object]"||(n=Object.keys(i),n.length!==1))return!1;a[t]=[n[0],i[n[0]]]}return!0}g(od,"resolveYamlPairs");function ld(e){if(e===null)return[];var t,r,i,n,a,o=e;for(a=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],n=Object.keys(i),a[t]=[n[0],i[n[0]]];return a}g(ld,"constructYamlPairs");var jb=new Xt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:od,construct:ld}),Gb=Object.prototype.hasOwnProperty;function cd(e){if(e===null)return!0;var t,r=e;for(t in r)if(Gb.call(r,t)&&r[t]!==null)return!1;return!0}g(cd,"resolveYamlSet");function hd(e){return e!==null?e:{}}g(hd,"constructYamlSet");var Vb=new Xt("tag:yaml.org,2002:set",{kind:"mapping",resolve:cd,construct:hd}),ud=Pb.extend({implicit:[Nb,zb],explicit:[Wb,Ub,jb,Vb]}),lr=Object.prototype.hasOwnProperty,Ma=1,fd=2,dd=3,Aa=4,to=1,Xb=2,fh=3,Zb=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Kb=/[\x85\u2028\u2029]/,Qb=/[,\[\]\{\}]/,pd=/^(?:!|!!|![a-z\-]+!)$/i,gd=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Do(e){return Object.prototype.toString.call(e)}g(Do,"_class");function ve(e){return e===10||e===13}g(ve,"is_EOL");function or(e){return e===9||e===32}g(or,"is_WHITE_SPACE");function te(e){return e===9||e===32||e===10||e===13}g(te,"is_WS_OR_EOL");function wr(e){return e===44||e===91||e===93||e===123||e===125}g(wr,"is_FLOW_INDICATOR");function md(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}g(md,"fromHexCode");function yd(e){return e===120?2:e===117?4:e===85?8:0}g(yd,"escapedHexLen");function xd(e){return 48<=e&&e<=57?e-48:-1}g(xd,"fromDecimalCode");function Oo(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}g(Oo,"simpleEscapeSequence");function bd(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}g(bd,"charFromCodepoint");var _d=new Array(256),Cd=new Array(256);for(gr=0;gr<256;gr++)_d[gr]=Oo(gr)?1:0,Cd[gr]=Oo(gr);var gr;function wd(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||ud,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}g(wd,"State$1");function Il(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=vb(r),new ce(t,r)}g(Il,"generateError");function rt(e,t){throw Il(e,t)}g(rt,"throwError");function gn(e,t){e.onWarning&&e.onWarning.call(null,Il(e,t))}g(gn,"throwWarning");var dh={YAML:g(function(t,r,i){var n,a,o;t.version!==null&&rt(t,"duplication of %YAML directive"),i.length!==1&&rt(t,"YAML directive accepts exactly one argument"),n=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),n===null&&rt(t,"ill-formed argument of the YAML directive"),a=parseInt(n[1],10),o=parseInt(n[2],10),a!==1&&rt(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&gn(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:g(function(t,r,i){var n,a;i.length!==2&&rt(t,"TAG directive accepts exactly two arguments"),n=i[0],a=i[1],pd.test(n)||rt(t,"ill-formed tag handle (first argument) of the TAG directive"),lr.call(t.tagMap,n)&&rt(t,'there is a previously declared suffix for "'+n+'" tag handle'),gd.test(a)||rt(t,"ill-formed tag prefix (second argument) of the TAG directive");try{a=decodeURIComponent(a)}catch{rt(t,"tag prefix is malformed: "+a)}t.tagMap[n]=a},"handleTagDirective")};function Ke(e,t,r,i){var n,a,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(n=0,a=s.length;n<a;n+=1)o=s.charCodeAt(n),o===9||32<=o&&o<=1114111||rt(e,"expected valid JSON character");else Zb.test(s)&&rt(e,"the stream contains non-printable characters");e.result+=s}}g(Ke,"captureSegment");function Ro(e,t,r,i){var n,a,o,s;for(Ft.isObject(r)||rt(e,"cannot merge mappings; the provided source object is unacceptable"),n=Object.keys(r),o=0,s=n.length;o<s;o+=1)a=n[o],lr.call(t,a)||(t[a]=r[a],i[a]=!0)}g(Ro,"mergeMappings");function kr(e,t,r,i,n,a,o,s,c){var l,h;if(Array.isArray(n))for(n=Array.prototype.slice.call(n),l=0,h=n.length;l<h;l+=1)Array.isArray(n[l])&&rt(e,"nested arrays are not supported inside keys"),typeof n=="object"&&Do(n[l])==="[object Object]"&&(n[l]="[object Object]");if(typeof n=="object"&&Do(n)==="[object Object]"&&(n="[object Object]"),n=String(n),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(a))for(l=0,h=a.length;l<h;l+=1)Ro(e,t,a[l],r);else Ro(e,t,a,r);else!e.json&&!lr.call(r,n)&&lr.call(t,n)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=c||e.position,rt(e,"duplicated mapping key")),n==="__proto__"?Object.defineProperty(t,n,{configurable:!0,enumerable:!0,writable:!0,value:a}):t[n]=a,delete r[n];return t}g(kr,"storeMappingPair");function ms(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):rt(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}g(ms,"readLineBreak");function Lt(e,t,r){for(var i=0,n=e.input.charCodeAt(e.position);n!==0;){for(;or(n);)n===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),n=e.input.charCodeAt(++e.position);if(t&&n===35)do n=e.input.charCodeAt(++e.position);while(n!==10&&n!==13&&n!==0);if(ve(n))for(ms(e),n=e.input.charCodeAt(e.position),i++,e.lineIndent=0;n===32;)e.lineIndent++,n=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&gn(e,"deficient indentation"),i}g(Lt,"skipSeparationSpace");function Ln(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||te(r)))}g(Ln,"testDocumentSeparator");function ys(e,t){t===1?e.result+=" ":t>1&&(e.result+=Ft.repeat(`
`,t-1))}g(ys,"writeFoldedLines");function kd(e,t,r){var i,n,a,o,s,c,l,h,u=e.kind,f=e.result,d;if(d=e.input.charCodeAt(e.position),te(d)||wr(d)||d===35||d===38||d===42||d===33||d===124||d===62||d===39||d===34||d===37||d===64||d===96||(d===63||d===45)&&(n=e.input.charCodeAt(e.position+1),te(n)||r&&wr(n)))return!1;for(e.kind="scalar",e.result="",a=o=e.position,s=!1;d!==0;){if(d===58){if(n=e.input.charCodeAt(e.position+1),te(n)||r&&wr(n))break}else if(d===35){if(i=e.input.charCodeAt(e.position-1),te(i))break}else{if(e.position===e.lineStart&&Ln(e)||r&&wr(d))break;if(ve(d))if(c=e.line,l=e.lineStart,h=e.lineIndent,Lt(e,!1,-1),e.lineIndent>=t){s=!0,d=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=c,e.lineStart=l,e.lineIndent=h;break}}s&&(Ke(e,a,o,!1),ys(e,e.line-c),a=o=e.position,s=!1),or(d)||(o=e.position+1),d=e.input.charCodeAt(++e.position)}return Ke(e,a,o,!1),e.result?!0:(e.kind=u,e.result=f,!1)}g(kd,"readPlainScalar");function vd(e,t){var r,i,n;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=n=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(Ke(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,n=e.position;else return!0;else ve(r)?(Ke(e,i,n,!0),ys(e,Lt(e,!1,t)),i=n=e.position):e.position===e.lineStart&&Ln(e)?rt(e,"unexpected end of the document within a single quoted scalar"):(e.position++,n=e.position);rt(e,"unexpected end of the stream within a single quoted scalar")}g(vd,"readSingleQuotedScalar");function Sd(e,t){var r,i,n,a,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return Ke(e,r,e.position,!0),e.position++,!0;if(s===92){if(Ke(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),ve(s))Lt(e,!1,t);else if(s<256&&_d[s])e.result+=Cd[s],e.position++;else if((o=yd(s))>0){for(n=o,a=0;n>0;n--)s=e.input.charCodeAt(++e.position),(o=md(s))>=0?a=(a<<4)+o:rt(e,"expected hexadecimal character");e.result+=bd(a),e.position++}else rt(e,"unknown escape sequence");r=i=e.position}else ve(s)?(Ke(e,r,i,!0),ys(e,Lt(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Ln(e)?rt(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}rt(e,"unexpected end of the stream within a double quoted scalar")}g(Sd,"readDoubleQuotedScalar");function Td(e,t){var r=!0,i,n,a,o=e.tag,s,c=e.anchor,l,h,u,f,d,p=Object.create(null),m,y,x,b;if(b=e.input.charCodeAt(e.position),b===91)h=93,d=!1,s=[];else if(b===123)h=125,d=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),b=e.input.charCodeAt(++e.position);b!==0;){if(Lt(e,!0,t),b=e.input.charCodeAt(e.position),b===h)return e.position++,e.tag=o,e.anchor=c,e.kind=d?"mapping":"sequence",e.result=s,!0;r?b===44&&rt(e,"expected the node content, but found ','"):rt(e,"missed comma between flow collection entries"),y=m=x=null,u=f=!1,b===63&&(l=e.input.charCodeAt(e.position+1),te(l)&&(u=f=!0,e.position++,Lt(e,!0,t))),i=e.line,n=e.lineStart,a=e.position,Lr(e,t,Ma,!1,!0),y=e.tag,m=e.result,Lt(e,!0,t),b=e.input.charCodeAt(e.position),(f||e.line===i)&&b===58&&(u=!0,b=e.input.charCodeAt(++e.position),Lt(e,!0,t),Lr(e,t,Ma,!1,!0),x=e.result),d?kr(e,s,p,y,m,x,i,n,a):u?s.push(kr(e,null,p,y,m,x,i,n,a)):s.push(m),Lt(e,!0,t),b=e.input.charCodeAt(e.position),b===44?(r=!0,b=e.input.charCodeAt(++e.position)):r=!1}rt(e,"unexpected end of the stream within a flow collection")}g(Td,"readFlowCollection");function Md(e,t){var r,i,n=to,a=!1,o=!1,s=t,c=0,l=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)to===n?n=u===43?fh:Xb:rt(e,"repeat of a chomping mode identifier");else if((h=xd(u))>=0)h===0?rt(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?rt(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(or(u)){do u=e.input.charCodeAt(++e.position);while(or(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!ve(u)&&u!==0)}for(;u!==0;){for(ms(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),ve(u)){c++;continue}if(e.lineIndent<s){n===fh?e.result+=Ft.repeat(`
`,a?1+c:c):n===to&&a&&(e.result+=`
`);break}for(i?or(u)?(l=!0,e.result+=Ft.repeat(`
`,a?1+c:c)):l?(l=!1,e.result+=Ft.repeat(`
`,c+1)):c===0?a&&(e.result+=" "):e.result+=Ft.repeat(`
`,c):e.result+=Ft.repeat(`
`,a?1+c:c),a=!0,o=!0,c=0,r=e.position;!ve(u)&&u!==0;)u=e.input.charCodeAt(++e.position);Ke(e,r,e.position,!1)}return!0}g(Md,"readBlockScalar");function Io(e,t){var r,i=e.tag,n=e.anchor,a=[],o,s=!1,c;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=a),c=e.input.charCodeAt(e.position);c!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,rt(e,"tab characters must not be used in indentation")),!(c!==45||(o=e.input.charCodeAt(e.position+1),!te(o))));){if(s=!0,e.position++,Lt(e,!0,-1)&&e.lineIndent<=t){a.push(null),c=e.input.charCodeAt(e.position);continue}if(r=e.line,Lr(e,t,dd,!1,!0),a.push(e.result),Lt(e,!0,-1),c=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&c!==0)rt(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=n,e.kind="sequence",e.result=a,!0):!1}g(Io,"readBlockSequence");function Ad(e,t,r){var i,n,a,o,s,c,l=e.tag,h=e.anchor,u={},f=Object.create(null),d=null,p=null,m=null,y=!1,x=!1,b;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),b=e.input.charCodeAt(e.position);b!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,rt(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),a=e.line,(b===63||b===58)&&te(i))b===63?(y&&(kr(e,u,f,d,p,null,o,s,c),d=p=m=null),x=!0,y=!0,n=!0):y?(y=!1,n=!0):rt(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,b=i;else{if(o=e.line,s=e.lineStart,c=e.position,!Lr(e,r,fd,!1,!0))break;if(e.line===a){for(b=e.input.charCodeAt(e.position);or(b);)b=e.input.charCodeAt(++e.position);if(b===58)b=e.input.charCodeAt(++e.position),te(b)||rt(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(kr(e,u,f,d,p,null,o,s,c),d=p=m=null),x=!0,y=!1,n=!1,d=e.tag,p=e.result;else if(x)rt(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=l,e.anchor=h,!0}else if(x)rt(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=l,e.anchor=h,!0}if((e.line===a||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,c=e.position),Lr(e,t,Aa,!0,n)&&(y?p=e.result:m=e.result),y||(kr(e,u,f,d,p,m,o,s,c),d=p=m=null),Lt(e,!0,-1),b=e.input.charCodeAt(e.position)),(e.line===a||e.lineIndent>t)&&b!==0)rt(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&kr(e,u,f,d,p,null,o,s,c),x&&(e.tag=l,e.anchor=h,e.kind="mapping",e.result=u),x}g(Ad,"readBlockMapping");function Ld(e){var t,r=!1,i=!1,n,a,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&rt(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,n="!!",o=e.input.charCodeAt(++e.position)):n="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(a=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):rt(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!te(o);)o===33&&(i?rt(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),pd.test(n)||rt(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);a=e.input.slice(t,e.position),Qb.test(a)&&rt(e,"tag suffix cannot contain flow indicator characters")}a&&!gd.test(a)&&rt(e,"tag name cannot contain such characters: "+a);try{a=decodeURIComponent(a)}catch{rt(e,"tag name is malformed: "+a)}return r?e.tag=a:lr.call(e.tagMap,n)?e.tag=e.tagMap[n]+a:n==="!"?e.tag="!"+a:n==="!!"?e.tag="tag:yaml.org,2002:"+a:rt(e,'undeclared tag handle "'+n+'"'),!0}g(Ld,"readTagProperty");function Bd(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&rt(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!te(r)&&!wr(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&rt(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}g(Bd,"readAnchorProperty");function Ed(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!te(i)&&!wr(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&rt(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),lr.call(e.anchorMap,r)||rt(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],Lt(e,!0,-1),!0}g(Ed,"readAlias");function Lr(e,t,r,i,n){var a,o,s,c=1,l=!1,h=!1,u,f,d,p,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,a=o=s=Aa===r||dd===r,i&&Lt(e,!0,-1)&&(l=!0,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)),c===1)for(;Ld(e)||Bd(e);)Lt(e,!0,-1)?(l=!0,s=a,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)):s=!1;if(s&&(s=l||n),(c===1||Aa===r)&&(Ma===r||fd===r?m=t:m=t+1,y=e.position-e.lineStart,c===1?s&&(Io(e,y)||Ad(e,y,m))||Td(e,m)?h=!0:(o&&Md(e,m)||vd(e,m)||Sd(e,m)?h=!0:Ed(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&rt(e,"alias node should not have any properties")):kd(e,m,Ma===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):c===0&&(h=s&&Io(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&rt(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,f=e.implicitTypes.length;u<f;u+=1)if(p=e.implicitTypes[u],p.resolve(e.result)){e.result=p.construct(e.result),e.tag=p.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(lr.call(e.typeMap[e.kind||"fallback"],e.tag))p=e.typeMap[e.kind||"fallback"][e.tag];else for(p=null,d=e.typeMap.multi[e.kind||"fallback"],u=0,f=d.length;u<f;u+=1)if(e.tag.slice(0,d[u].tag.length)===d[u].tag){p=d[u];break}p||rt(e,"unknown tag !<"+e.tag+">"),e.result!==null&&p.kind!==e.kind&&rt(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+p.kind+'", not "'+e.kind+'"'),p.resolve(e.result,e.tag)?(e.result=p.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):rt(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}g(Lr,"composeNode");function Fd(e){var t=e.position,r,i,n,a=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(Lt(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(a=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!te(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),n=[],i.length<1&&rt(e,"directive name must not be less than one character in length");o!==0;){for(;or(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!ve(o));break}if(ve(o))break;for(r=e.position;o!==0&&!te(o);)o=e.input.charCodeAt(++e.position);n.push(e.input.slice(r,e.position))}o!==0&&ms(e),lr.call(dh,i)?dh[i](e,i,n):gn(e,'unknown document directive "'+i+'"')}if(Lt(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,Lt(e,!0,-1)):a&&rt(e,"directives end mark is expected"),Lr(e,e.lineIndent-1,Aa,!1,!0),Lt(e,!0,-1),e.checkLineBreaks&&Kb.test(e.input.slice(t,e.position))&&gn(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Ln(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,Lt(e,!0,-1));return}if(e.position<e.length-1)rt(e,"end of the stream or a document separator is expected");else return}g(Fd,"readDocument");function Pl(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new wd(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,rt(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)Fd(r);return r.documents}g(Pl,"loadDocuments");function Jb(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=Pl(e,r);if(typeof t!="function")return i;for(var n=0,a=i.length;n<a;n+=1)t(i[n])}g(Jb,"loadAll$1");function $d(e,t){var r=Pl(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new ce("expected a single document in the stream, but found more")}}g($d,"load$1");var t1=$d,e1={load:t1},Dd=Object.prototype.toString,Od=Object.prototype.hasOwnProperty,Nl=65279,r1=9,mn=10,i1=13,n1=32,a1=33,s1=34,Po=35,o1=37,l1=38,c1=39,h1=42,Rd=44,u1=45,La=58,f1=61,d1=62,p1=63,g1=64,Id=91,Pd=93,m1=96,Nd=123,y1=124,zd=125,Zt={};Zt[0]="\\0";Zt[7]="\\a";Zt[8]="\\b";Zt[9]="\\t";Zt[10]="\\n";Zt[11]="\\v";Zt[12]="\\f";Zt[13]="\\r";Zt[27]="\\e";Zt[34]='\\"';Zt[92]="\\\\";Zt[133]="\\N";Zt[160]="\\_";Zt[8232]="\\L";Zt[8233]="\\P";var x1=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],b1=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Wd(e,t){var r,i,n,a,o,s,c;if(t===null)return{};for(r={},i=Object.keys(t),n=0,a=i.length;n<a;n+=1)o=i[n],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),c=e.compiledTypeMap.fallback[o],c&&Od.call(c.styleAliases,s)&&(s=c.styleAliases[s]),r[o]=s;return r}g(Wd,"compileStyleMap");function qd(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new ce("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Ft.repeat("0",i-t.length)+t}g(qd,"encodeHex");var _1=1,yn=2;function Hd(e){this.schema=e.schema||ud,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Ft.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Wd(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?yn:_1,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}g(Hd,"State");function No(e,t){for(var r=Ft.repeat(" ",t),i=0,n=-1,a="",o,s=e.length;i<s;)n=e.indexOf(`
`,i),n===-1?(o=e.slice(i),i=s):(o=e.slice(i,n+1),i=n+1),o.length&&o!==`
`&&(a+=r),a+=o;return a}g(No,"indentString");function Ba(e,t){return`
`+Ft.repeat(" ",e.indent*t)}g(Ba,"generateNextLine");function Ud(e,t){var r,i,n;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(n=e.implicitTypes[r],n.resolve(t))return!0;return!1}g(Ud,"testImplicitResolving");function xn(e){return e===n1||e===r1}g(xn,"isWhitespace");function _i(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Nl||65536<=e&&e<=1114111}g(_i,"isPrintable");function zo(e){return _i(e)&&e!==Nl&&e!==i1&&e!==mn}g(zo,"isNsCharOrWhitespace");function Wo(e,t,r){var i=zo(e),n=i&&!xn(e);return(r?i:i&&e!==Rd&&e!==Id&&e!==Pd&&e!==Nd&&e!==zd)&&e!==Po&&!(t===La&&!n)||zo(t)&&!xn(t)&&e===Po||t===La&&n}g(Wo,"isPlainSafe");function Yd(e){return _i(e)&&e!==Nl&&!xn(e)&&e!==u1&&e!==p1&&e!==La&&e!==Rd&&e!==Id&&e!==Pd&&e!==Nd&&e!==zd&&e!==Po&&e!==l1&&e!==h1&&e!==a1&&e!==y1&&e!==f1&&e!==d1&&e!==c1&&e!==s1&&e!==o1&&e!==g1&&e!==m1}g(Yd,"isPlainSafeFirst");function jd(e){return!xn(e)&&e!==La}g(jd,"isPlainSafeLast");function Qr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}g(Qr,"codePointAt");function zl(e){var t=/^\n* /;return t.test(e)}g(zl,"needIndentIndicator");var Gd=1,qo=2,Vd=3,Xd=4,Zr=5;function Zd(e,t,r,i,n,a,o,s){var c,l=0,h=null,u=!1,f=!1,d=i!==-1,p=-1,m=Yd(Qr(e,0))&&jd(Qr(e,e.length-1));if(t||o)for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Qr(e,c),!_i(l))return Zr;m=m&&Wo(l,h,s),h=l}else{for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Qr(e,c),l===mn)u=!0,d&&(f=f||c-p-1>i&&e[p+1]!==" ",p=c);else if(!_i(l))return Zr;m=m&&Wo(l,h,s),h=l}f=f||d&&c-p-1>i&&e[p+1]!==" "}return!u&&!f?m&&!o&&!n(e)?Gd:a===yn?Zr:qo:r>9&&zl(e)?Zr:o?a===yn?Zr:qo:f?Xd:Vd}g(Zd,"chooseScalarStyle");function Kd(e,t,r,i,n){e.dump=function(){if(t.length===0)return e.quotingType===yn?'""':"''";if(!e.noCompatMode&&(x1.indexOf(t)!==-1||b1.test(t)))return e.quotingType===yn?'"'+t+'"':"'"+t+"'";var a=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-a),s=i||e.flowLevel>-1&&r>=e.flowLevel;function c(l){return Ud(e,l)}switch(g(c,"testAmbiguity"),Zd(t,s,e.indent,o,c,e.quotingType,e.forceQuotes&&!i,n)){case Gd:return t;case qo:return"'"+t.replace(/'/g,"''")+"'";case Vd:return"|"+Ho(t,e.indent)+Uo(No(t,a));case Xd:return">"+Ho(t,e.indent)+Uo(No(Qd(t,o),a));case Zr:return'"'+Jd(t)+'"';default:throw new ce("impossible error: invalid scalar style")}}()}g(Kd,"writeScalar");function Ho(e,t){var r=zl(e)?String(t):"",i=e[e.length-1]===`
`,n=i&&(e[e.length-2]===`
`||e===`
`),a=n?"+":i?"":"-";return r+a+`
`}g(Ho,"blockHeader");function Uo(e){return e[e.length-1]===`
`?e.slice(0,-1):e}g(Uo,"dropEndingNewline");function Qd(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var l=e.indexOf(`
`);return l=l!==-1?l:e.length,r.lastIndex=l,Yo(e.slice(0,l),t)}(),n=e[0]===`
`||e[0]===" ",a,o;o=r.exec(e);){var s=o[1],c=o[2];a=c[0]===" ",i+=s+(!n&&!a&&c!==""?`
`:"")+Yo(c,t),n=a}return i}g(Qd,"foldString");function Yo(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,n=0,a,o=0,s=0,c="";i=r.exec(e);)s=i.index,s-n>t&&(a=o>n?o:s,c+=`
`+e.slice(n,a),n=a+1),o=s;return c+=`
`,e.length-n>t&&o>n?c+=e.slice(n,o)+`
`+e.slice(o+1):c+=e.slice(n),c.slice(1)}g(Yo,"foldLine");function Jd(e){for(var t="",r=0,i,n=0;n<e.length;r>=65536?n+=2:n++)r=Qr(e,n),i=Zt[r],!i&&_i(r)?(t+=e[n],r>=65536&&(t+=e[n+1])):t+=i||qd(r);return t}g(Jd,"escapeString");function tp(e,t,r){var i="",n=e.tag,a,o,s;for(a=0,o=r.length;a<o;a+=1)s=r[a],e.replacer&&(s=e.replacer.call(r,String(a),s)),(Ie(e,t,s,!1,!1)||typeof s>"u"&&Ie(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=n,e.dump="["+i+"]"}g(tp,"writeFlowSequence");function jo(e,t,r,i){var n="",a=e.tag,o,s,c;for(o=0,s=r.length;o<s;o+=1)c=r[o],e.replacer&&(c=e.replacer.call(r,String(o),c)),(Ie(e,t+1,c,!0,!0,!1,!0)||typeof c>"u"&&Ie(e,t+1,null,!0,!0,!1,!0))&&((!i||n!=="")&&(n+=Ba(e,t)),e.dump&&mn===e.dump.charCodeAt(0)?n+="-":n+="- ",n+=e.dump);e.tag=a,e.dump=n||"[]"}g(jo,"writeBlockSequence");function ep(e,t,r){var i="",n=e.tag,a=Object.keys(r),o,s,c,l,h;for(o=0,s=a.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),c=a[o],l=r[c],e.replacer&&(l=e.replacer.call(r,c,l)),Ie(e,t,c,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),Ie(e,t,l,!1,!1)&&(h+=e.dump,i+=h));e.tag=n,e.dump="{"+i+"}"}g(ep,"writeFlowMapping");function rp(e,t,r,i){var n="",a=e.tag,o=Object.keys(r),s,c,l,h,u,f;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new ce("sortKeys must be a boolean or a function");for(s=0,c=o.length;s<c;s+=1)f="",(!i||n!=="")&&(f+=Ba(e,t)),l=o[s],h=r[l],e.replacer&&(h=e.replacer.call(r,l,h)),Ie(e,t+1,l,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&mn===e.dump.charCodeAt(0)?f+="?":f+="? "),f+=e.dump,u&&(f+=Ba(e,t)),Ie(e,t+1,h,!0,u)&&(e.dump&&mn===e.dump.charCodeAt(0)?f+=":":f+=": ",f+=e.dump,n+=f));e.tag=a,e.dump=n||"{}"}g(rp,"writeBlockMapping");function Go(e,t,r){var i,n,a,o,s,c;for(n=r?e.explicitTypes:e.implicitTypes,a=0,o=n.length;a<o;a+=1)if(s=n[a],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(c=e.styleMap[s.tag]||s.defaultStyle,Dd.call(s.represent)==="[object Function]")i=s.represent(t,c);else if(Od.call(s.represent,c))i=s.represent[c](t,c);else throw new ce("!<"+s.tag+'> tag resolver accepts not "'+c+'" style');e.dump=i}return!0}return!1}g(Go,"detectType");function Ie(e,t,r,i,n,a,o){e.tag=null,e.dump=r,Go(e,r,!1)||Go(e,r,!0);var s=Dd.call(e.dump),c=i,l;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,f;if(h&&(u=e.duplicates.indexOf(r),f=u!==-1),(e.tag!==null&&e.tag!=="?"||f||e.indent!==2&&t>0)&&(n=!1),f&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&f&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(rp(e,t,e.dump,n),f&&(e.dump="&ref_"+u+e.dump)):(ep(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?jo(e,t-1,e.dump,n):jo(e,t,e.dump,n),f&&(e.dump="&ref_"+u+e.dump)):(tp(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&Kd(e,e.dump,t,a,c);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new ce("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(l=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?l="!"+l:l.slice(0,18)==="tag:yaml.org,2002:"?l="!!"+l.slice(18):l="!<"+l+">",e.dump=l+" "+e.dump)}return!0}g(Ie,"writeNode");function ip(e,t){var r=[],i=[],n,a;for(Ea(e,r,i),n=0,a=i.length;n<a;n+=1)t.duplicates.push(r[i[n]]);t.usedDuplicates=new Array(a)}g(ip,"getDuplicateReferences");function Ea(e,t,r){var i,n,a;if(e!==null&&typeof e=="object")if(n=t.indexOf(e),n!==-1)r.indexOf(n)===-1&&r.push(n);else if(t.push(e),Array.isArray(e))for(n=0,a=e.length;n<a;n+=1)Ea(e[n],t,r);else for(i=Object.keys(e),n=0,a=i.length;n<a;n+=1)Ea(e[i[n]],t,r)}g(Ea,"inspectNode");function C1(e,t){t=t||{};var r=new Hd(t);r.noRefs||ip(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),Ie(r,0,i,!0,!0)?r.dump+`
`:""}g(C1,"dump$1");function w1(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}g(w1,"renamed");var k1=Vf,v1=e1.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/var ge={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function tn(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=St(e),t=St(t);const[r,i]=[e.x,e.y],[n,a]=[t.x,t.y],o=n-r,s=a-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}g(tn,"calculateDeltaAndAngle");var St=g(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),S1=g(e=>({x:g(function(t,r,i){let n=0;const a=St(i[0]).x<St(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(ge,e.arrowTypeStart)){const{angle:d,deltaX:p}=tn(i[0],i[1]);n=ge[e.arrowTypeStart]*Math.cos(d)*(p>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ge,e.arrowTypeEnd)){const{angle:d,deltaX:p}=tn(i[i.length-1],i[i.length-2]);n=ge[e.arrowTypeEnd]*Math.cos(d)*(p>=0?1:-1)}const o=Math.abs(St(t).x-St(i[i.length-1]).x),s=Math.abs(St(t).y-St(i[i.length-1]).y),c=Math.abs(St(t).x-St(i[0]).x),l=Math.abs(St(t).y-St(i[0]).y),h=ge[e.arrowTypeStart],u=ge[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let d=u+f-o;d*=a==="right"?-1:1,n-=d}if(c<h&&c>0&&l<h){let d=h+f-c;d*=a==="right"?-1:1,n+=d}return St(t).x+n},"x"),y:g(function(t,r,i){let n=0;const a=St(i[0]).y<St(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(ge,e.arrowTypeStart)){const{angle:d,deltaY:p}=tn(i[0],i[1]);n=ge[e.arrowTypeStart]*Math.abs(Math.sin(d))*(p>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ge,e.arrowTypeEnd)){const{angle:d,deltaY:p}=tn(i[i.length-1],i[i.length-2]);n=ge[e.arrowTypeEnd]*Math.abs(Math.sin(d))*(p>=0?1:-1)}const o=Math.abs(St(t).y-St(i[i.length-1]).y),s=Math.abs(St(t).x-St(i[i.length-1]).x),c=Math.abs(St(t).y-St(i[0]).y),l=Math.abs(St(t).x-St(i[0]).x),h=ge[e.arrowTypeStart],u=ge[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let d=u+f-o;d*=a==="up"?-1:1,n-=d}if(c<h&&c>0&&l<h){let d=h+f-c;d*=a==="up"?-1:1,n+=d}return St(t).y+n},"y")}),"getLineFunctionsWithOffset"),Wl=g(({flowchart:e})=>{var n,a;const t=((n=e==null?void 0:e.subGraphTitleMargin)==null?void 0:n.top)??0,r=((a=e==null?void 0:e.subGraphTitleMargin)==null?void 0:a.bottom)??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins");const T1=Object.freeze({left:0,top:0,width:16,height:16}),Fa=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),np=Object.freeze({...T1,...Fa}),M1=Object.freeze({...np,body:"",hidden:!1}),A1=Object.freeze({width:null,height:null}),L1=Object.freeze({...A1,...Fa}),B1=(e,t,r,i="")=>{const n=e.split(":");if(e.slice(0,1)==="@"){if(n.length<2||n.length>3)return null;i=n.shift().slice(1)}if(n.length>3||!n.length)return null;if(n.length>1){const s=n.pop(),c=n.pop(),l={provider:n.length>0?n[0]:i,prefix:c,name:s};return eo(l)?l:null}const a=n[0],o=a.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return eo(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:a};return eo(s,r)?s:null}return null},eo=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function E1(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function ph(e,t){const r=E1(e,t);for(const i in M1)i in Fa?i in e&&!(i in r)&&(r[i]=Fa[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function F1(e,t){const r=e.icons,i=e.aliases||Object.create(null),n=Object.create(null);function a(o){if(r[o])return n[o]=[];if(!(o in n)){n[o]=null;const s=i[o]&&i[o].parent,c=s&&a(s);c&&(n[o]=[s].concat(c))}return n[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(a),n}function gh(e,t,r){const i=e.icons,n=e.aliases||Object.create(null);let a={};function o(s){a=ph(i[s]||n[s],a)}return o(t),r.forEach(o),ph(e,a)}function $1(e,t){if(e.icons[t])return gh(e,t,[]);const r=F1(e,[t])[t];return r?gh(e,t,r):null}const D1=/(-?[0-9.]*[0-9]+[0-9.]*)/g,O1=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function mh(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(D1);if(i===null||!i.length)return e;const n=[];let a=i.shift(),o=O1.test(a);for(;;){if(o){const s=parseFloat(a);isNaN(s)?n.push(a):n.push(Math.ceil(s*t*r)/r)}else n.push(a);if(a=i.shift(),a===void 0)return n.join("");o=!o}}function R1(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const n=e.indexOf(">",i),a=e.indexOf("</"+t);if(n===-1||a===-1)break;const o=e.indexOf(">",a);if(o===-1)break;r+=e.slice(n+1,a).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function I1(e,t){return e?"<defs>"+e+"</defs>"+t:t}function P1(e,t,r){const i=R1(e);return I1(i.defs,t+i.content+r)}const N1=e=>e==="unset"||e==="undefined"||e==="none";function z1(e,t){const r={...np,...e},i={...L1,...t},n={left:r.left,top:r.top,width:r.width,height:r.height};let a=r.body;[r,i].forEach(m=>{const y=[],x=m.hFlip,b=m.vFlip;let C=m.rotate;x?b?C+=2:(y.push("translate("+(n.width+n.left).toString()+" "+(0-n.top).toString()+")"),y.push("scale(-1 1)"),n.top=n.left=0):b&&(y.push("translate("+(0-n.left).toString()+" "+(n.height+n.top).toString()+")"),y.push("scale(1 -1)"),n.top=n.left=0);let v;switch(C<0&&(C-=Math.floor(C/4)*4),C=C%4,C){case 1:v=n.height/2+n.top,y.unshift("rotate(90 "+v.toString()+" "+v.toString()+")");break;case 2:y.unshift("rotate(180 "+(n.width/2+n.left).toString()+" "+(n.height/2+n.top).toString()+")");break;case 3:v=n.width/2+n.left,y.unshift("rotate(-90 "+v.toString()+" "+v.toString()+")");break}C%2===1&&(n.left!==n.top&&(v=n.left,n.left=n.top,n.top=v),n.width!==n.height&&(v=n.width,n.width=n.height,n.height=v)),y.length&&(a=P1(a,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,c=n.width,l=n.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?l:s,h=mh(u,c/l)):(h=o==="auto"?c:o,u=s===null?mh(h,l/c):s==="auto"?l:s);const f={},d=(m,y)=>{N1(y)||(f[m]=y.toString())};d("width",h),d("height",u);const p=[n.left,n.top,c,l];return f.viewBox=p.join(" "),{attributes:f,viewBox:p,body:a}}const W1=/\sid="(\S+)"/g,q1="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let H1=0;function U1(e,t=q1){const r=[];let i;for(;i=W1.exec(e);)r.push(i[1]);if(!r.length)return e;const n="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(a=>{const o=typeof t=="function"?t(a):t+(H1++).toString(),s=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+n+"$3")}),e=e.replace(new RegExp(n,"g"),""),e}function Y1(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}var j1={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},Vo=new Map,ap=new Map,G1=g(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(R.debug("Registering icon pack:",t.name),"loader"in t)ap.set(t.name,t.loader);else if("icons"in t)Vo.set(t.name,t.icons);else throw R.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),V1=g(async(e,t)=>{const r=B1(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let n=Vo.get(i);if(!n){const o=ap.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{n={...await o(),prefix:i},Vo.set(i,n)}catch(s){throw R.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const a=$1(n,r.name);if(!a)throw new Error(`Icon not found: ${e}`);return a},"getRegisteredIconData"),xs=g(async(e,t)=>{let r;try{r=await V1(e,t==null?void 0:t.fallbackPrefix)}catch(a){R.error(a),r=j1}const i=z1(r,t);return Y1(U1(i.body),i.attributes)},"getIconSVG"),qi={},Et={},yh;function X1(){return yh||(yh=1,Object.defineProperty(Et,"__esModule",{value:!0}),Et.BLANK_URL=Et.relativeFirstCharacters=Et.whitespaceEscapeCharsRegex=Et.urlSchemeRegex=Et.ctrlCharactersRegex=Et.htmlCtrlEntityRegex=Et.htmlEntitiesRegex=Et.invalidProtocolRegex=void 0,Et.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im,Et.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g,Et.htmlCtrlEntityRegex=/&(newline|tab);/gi,Et.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,Et.urlSchemeRegex=/^.+(:|&colon;)/gim,Et.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g,Et.relativeFirstCharacters=[".","/"],Et.BLANK_URL="about:blank"),Et}var xh;function Z1(){if(xh)return qi;xh=1,Object.defineProperty(qi,"__esModule",{value:!0}),qi.sanitizeUrl=void 0;var e=X1();function t(o){return e.relativeFirstCharacters.indexOf(o[0])>-1}function r(o){var s=o.replace(e.ctrlCharactersRegex,"");return s.replace(e.htmlEntitiesRegex,function(c,l){return String.fromCharCode(l)})}function i(o){return URL.canParse(o)}function n(o){try{return decodeURIComponent(o)}catch{return o}}function a(o){if(!o)return e.BLANK_URL;var s,c=n(o.trim());do c=r(c).replace(e.htmlCtrlEntityRegex,"").replace(e.ctrlCharactersRegex,"").replace(e.whitespaceEscapeCharsRegex,"").trim(),c=n(c),s=c.match(e.ctrlCharactersRegex)||c.match(e.htmlEntitiesRegex)||c.match(e.htmlCtrlEntityRegex)||c.match(e.whitespaceEscapeCharsRegex);while(s&&s.length>0);var l=c;if(!l)return e.BLANK_URL;if(t(l))return l;var h=l.trimStart(),u=h.match(e.urlSchemeRegex);if(!u)return l;var f=u[0].toLowerCase().trim();if(e.invalidProtocolRegex.test(f))return e.BLANK_URL;var d=h.replace(/\\/g,"/");if(f==="mailto:"||f.includes("://"))return d;if(f==="http:"||f==="https:"){if(!i(d))return e.BLANK_URL;var p=new URL(d);return p.protocol=p.protocol.toLowerCase(),p.hostname=p.hostname.toLowerCase(),p.toString()}return d}return qi.sanitizeUrl=a,qi}var K1=Z1();function ha(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Q1(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ql(e){let t,r,i;e.length!==2?(t=ha,r=(s,c)=>ha(e(s),c),i=(s,c)=>e(s)-c):(t=e===ha||e===Q1?e:J1,r=e,i=e);function n(s,c,l=0,h=s.length){if(l<h){if(t(c,c)!==0)return h;do{const u=l+h>>>1;r(s[u],c)<0?l=u+1:h=u}while(l<h)}return l}function a(s,c,l=0,h=s.length){if(l<h){if(t(c,c)!==0)return h;do{const u=l+h>>>1;r(s[u],c)<=0?l=u+1:h=u}while(l<h)}return l}function o(s,c,l=0,h=s.length){const u=n(s,c,l,h-1);return u>l&&i(s[u-1],c)>-i(s[u],c)?u-1:u}return{left:n,center:o,right:a}}function J1(){return 0}function t2(e){return e===null?NaN:+e}const e2=ql(ha),r2=e2.right;ql(t2).center;class bh extends Map{constructor(t,r=a2){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[i,n]of t)this.set(i,n)}get(t){return super.get(_h(this,t))}has(t){return super.has(_h(this,t))}set(t,r){return super.set(i2(this,t),r)}delete(t){return super.delete(n2(this,t))}}function _h({_intern:e,_key:t},r){const i=t(r);return e.has(i)?e.get(i):r}function i2({_intern:e,_key:t},r){const i=t(r);return e.has(i)?e.get(i):(e.set(i,r),r)}function n2({_intern:e,_key:t},r){const i=t(r);return e.has(i)&&(r=e.get(r),e.delete(i)),r}function a2(e){return e!==null&&typeof e=="object"?e.valueOf():e}const s2=Math.sqrt(50),o2=Math.sqrt(10),l2=Math.sqrt(2);function $a(e,t,r){const i=(t-e)/Math.max(0,r),n=Math.floor(Math.log10(i)),a=i/Math.pow(10,n),o=a>=s2?10:a>=o2?5:a>=l2?2:1;let s,c,l;return n<0?(l=Math.pow(10,-n)/o,s=Math.round(e*l),c=Math.round(t*l),s/l<e&&++s,c/l>t&&--c,l=-l):(l=Math.pow(10,n)*o,s=Math.round(e/l),c=Math.round(t/l),s*l<e&&++s,c*l>t&&--c),c<s&&.5<=r&&r<2?$a(e,t,r*2):[s,c,l]}function c2(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const i=t<e,[n,a,o]=i?$a(t,e,r):$a(e,t,r);if(!(a>=n))return[];const s=a-n+1,c=new Array(s);if(i)if(o<0)for(let l=0;l<s;++l)c[l]=(a-l)/-o;else for(let l=0;l<s;++l)c[l]=(a-l)*o;else if(o<0)for(let l=0;l<s;++l)c[l]=(n+l)/-o;else for(let l=0;l<s;++l)c[l]=(n+l)*o;return c}function Xo(e,t,r){return t=+t,e=+e,r=+r,$a(e,t,r)[2]}function Zo(e,t,r){t=+t,e=+e,r=+r;const i=t<e,n=i?Xo(t,e,r):Xo(e,t,r);return(i?-1:1)*(n<0?1/-n:n)}function c3(e,t){let r;if(t===void 0)for(const i of e)i!=null&&(r<i||r===void 0&&i>=i)&&(r=i);else{let i=-1;for(let n of e)(n=t(n,++i,e))!=null&&(r<n||r===void 0&&n>=n)&&(r=n)}return r}function h3(e,t){let r;if(t===void 0)for(const i of e)i!=null&&(r>i||r===void 0&&i>=i)&&(r=i);else{let i=-1;for(let n of e)(n=t(n,++i,e))!=null&&(r>n||r===void 0&&n>=n)&&(r=n)}return r}function h2(e,t,r){e=+e,t=+t,r=(n=arguments.length)<2?(t=e,e=0,1):n<3?1:+r;for(var i=-1,n=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(n);++i<n;)a[i]=e+i*r;return a}function u2(e){return e}var ua=1,ro=2,Ko=3,jn=4,Ch=1e-6;function f2(e){return"translate("+e+",0)"}function d2(e){return"translate(0,"+e+")"}function p2(e){return t=>+e(t)}function g2(e,t){return t=Math.max(0,e.bandwidth()-t*2)/2,e.round()&&(t=Math.round(t)),r=>+e(r)+t}function m2(){return!this.__axis}function sp(e,t){var r=[],i=null,n=null,a=6,o=6,s=3,c=typeof window<"u"&&window.devicePixelRatio>1?0:.5,l=e===ua||e===jn?-1:1,h=e===jn||e===ro?"x":"y",u=e===ua||e===Ko?f2:d2;function f(d){var p=i??(t.ticks?t.ticks.apply(t,r):t.domain()),m=n??(t.tickFormat?t.tickFormat.apply(t,r):u2),y=Math.max(a,0)+s,x=t.range(),b=+x[0]+c,C=+x[x.length-1]+c,v=(t.bandwidth?g2:p2)(t.copy(),c),k=d.selection?d.selection():d,_=k.selectAll(".domain").data([null]),w=k.selectAll(".tick").data(p,t).order(),O=w.exit(),N=w.enter().append("g").attr("class","tick"),$=w.select("line"),S=w.select("text");_=_.merge(_.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),w=w.merge(N),$=$.merge(N.append("line").attr("stroke","currentColor").attr(h+"2",l*a)),S=S.merge(N.append("text").attr("fill","currentColor").attr(h,l*y).attr("dy",e===ua?"0em":e===Ko?"0.71em":"0.32em")),d!==k&&(_=_.transition(d),w=w.transition(d),$=$.transition(d),S=S.transition(d),O=O.transition(d).attr("opacity",Ch).attr("transform",function(I){return isFinite(I=v(I))?u(I+c):this.getAttribute("transform")}),N.attr("opacity",Ch).attr("transform",function(I){var E=this.parentNode.__axis;return u((E&&isFinite(E=E(I))?E:v(I))+c)})),O.remove(),_.attr("d",e===jn||e===ro?o?"M"+l*o+","+b+"H"+c+"V"+C+"H"+l*o:"M"+c+","+b+"V"+C:o?"M"+b+","+l*o+"V"+c+"H"+C+"V"+l*o:"M"+b+","+c+"H"+C),w.attr("opacity",1).attr("transform",function(I){return u(v(I)+c)}),$.attr(h+"2",l*a),S.attr(h,l*y).text(m),k.filter(m2).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",e===ro?"start":e===jn?"end":"middle"),k.each(function(){this.__axis=v})}return f.scale=function(d){return arguments.length?(t=d,f):t},f.ticks=function(){return r=Array.from(arguments),f},f.tickArguments=function(d){return arguments.length?(r=d==null?[]:Array.from(d),f):r.slice()},f.tickValues=function(d){return arguments.length?(i=d==null?null:Array.from(d),f):i&&i.slice()},f.tickFormat=function(d){return arguments.length?(n=d,f):n},f.tickSize=function(d){return arguments.length?(a=o=+d,f):a},f.tickSizeInner=function(d){return arguments.length?(a=+d,f):a},f.tickSizeOuter=function(d){return arguments.length?(o=+d,f):o},f.tickPadding=function(d){return arguments.length?(s=+d,f):s},f.offset=function(d){return arguments.length?(c=+d,f):c},f}function u3(e){return sp(ua,e)}function f3(e){return sp(Ko,e)}var y2={value:()=>{}};function op(){for(var e=0,t=arguments.length,r={},i;e<t;++e){if(!(i=arguments[e]+"")||i in r||/[\s.]/.test(i))throw new Error("illegal type: "+i);r[i]=[]}return new fa(r)}function fa(e){this._=e}function x2(e,t){return e.trim().split(/^|\s+/).map(function(r){var i="",n=r.indexOf(".");if(n>=0&&(i=r.slice(n+1),r=r.slice(0,n)),r&&!t.hasOwnProperty(r))throw new Error("unknown type: "+r);return{type:r,name:i}})}fa.prototype=op.prototype={constructor:fa,on:function(e,t){var r=this._,i=x2(e+"",r),n,a=-1,o=i.length;if(arguments.length<2){for(;++a<o;)if((n=(e=i[a]).type)&&(n=b2(r[n],e.name)))return n;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++a<o;)if(n=(e=i[a]).type)r[n]=wh(r[n],e.name,t);else if(t==null)for(n in r)r[n]=wh(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var r in t)e[r]=t[r].slice();return new fa(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var r=new Array(n),i=0,n,a;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(a=this._[e],i=0,n=a.length;i<n;++i)a[i].value.apply(t,r)},apply:function(e,t,r){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var i=this._[e],n=0,a=i.length;n<a;++n)i[n].value.apply(t,r)}};function b2(e,t){for(var r=0,i=e.length,n;r<i;++r)if((n=e[r]).name===t)return n.value}function wh(e,t,r){for(var i=0,n=e.length;i<n;++i)if(e[i].name===t){e[i]=y2,e=e.slice(0,i).concat(e.slice(i+1));break}return r!=null&&e.push({name:t,value:r}),e}var Qo="http://www.w3.org/1999/xhtml";const kh={svg:"http://www.w3.org/2000/svg",xhtml:Qo,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function bs(e){var t=e+="",r=t.indexOf(":");return r>=0&&(t=e.slice(0,r))!=="xmlns"&&(e=e.slice(r+1)),kh.hasOwnProperty(t)?{space:kh[t],local:e}:e}function _2(e){return function(){var t=this.ownerDocument,r=this.namespaceURI;return r===Qo&&t.documentElement.namespaceURI===Qo?t.createElement(e):t.createElementNS(r,e)}}function C2(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function lp(e){var t=bs(e);return(t.local?C2:_2)(t)}function w2(){}function Hl(e){return e==null?w2:function(){return this.querySelector(e)}}function k2(e){typeof e!="function"&&(e=Hl(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=new Array(o),c,l,h=0;h<o;++h)(c=a[h])&&(l=e.call(c,c.__data__,h,a))&&("__data__"in c&&(l.__data__=c.__data__),s[h]=l);return new de(i,this._parents)}function v2(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function S2(){return[]}function cp(e){return e==null?S2:function(){return this.querySelectorAll(e)}}function T2(e){return function(){return v2(e.apply(this,arguments))}}function M2(e){typeof e=="function"?e=T2(e):e=cp(e);for(var t=this._groups,r=t.length,i=[],n=[],a=0;a<r;++a)for(var o=t[a],s=o.length,c,l=0;l<s;++l)(c=o[l])&&(i.push(e.call(c,c.__data__,l,o)),n.push(c));return new de(i,n)}function hp(e){return function(){return this.matches(e)}}function up(e){return function(t){return t.matches(e)}}var A2=Array.prototype.find;function L2(e){return function(){return A2.call(this.children,e)}}function B2(){return this.firstElementChild}function E2(e){return this.select(e==null?B2:L2(typeof e=="function"?e:up(e)))}var F2=Array.prototype.filter;function $2(){return Array.from(this.children)}function D2(e){return function(){return F2.call(this.children,e)}}function O2(e){return this.selectAll(e==null?$2:D2(typeof e=="function"?e:up(e)))}function R2(e){typeof e!="function"&&(e=hp(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=[],c,l=0;l<o;++l)(c=a[l])&&e.call(c,c.__data__,l,a)&&s.push(c);return new de(i,this._parents)}function fp(e){return new Array(e.length)}function I2(){return new de(this._enter||this._groups.map(fp),this._parents)}function Da(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Da.prototype={constructor:Da,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function P2(e){return function(){return e}}function N2(e,t,r,i,n,a){for(var o=0,s,c=t.length,l=a.length;o<l;++o)(s=t[o])?(s.__data__=a[o],i[o]=s):r[o]=new Da(e,a[o]);for(;o<c;++o)(s=t[o])&&(n[o]=s)}function z2(e,t,r,i,n,a,o){var s,c,l=new Map,h=t.length,u=a.length,f=new Array(h),d;for(s=0;s<h;++s)(c=t[s])&&(f[s]=d=o.call(c,c.__data__,s,t)+"",l.has(d)?n[s]=c:l.set(d,c));for(s=0;s<u;++s)d=o.call(e,a[s],s,a)+"",(c=l.get(d))?(i[s]=c,c.__data__=a[s],l.delete(d)):r[s]=new Da(e,a[s]);for(s=0;s<h;++s)(c=t[s])&&l.get(f[s])===c&&(n[s]=c)}function W2(e){return e.__data__}function q2(e,t){if(!arguments.length)return Array.from(this,W2);var r=t?z2:N2,i=this._parents,n=this._groups;typeof e!="function"&&(e=P2(e));for(var a=n.length,o=new Array(a),s=new Array(a),c=new Array(a),l=0;l<a;++l){var h=i[l],u=n[l],f=u.length,d=H2(e.call(h,h&&h.__data__,l,i)),p=d.length,m=s[l]=new Array(p),y=o[l]=new Array(p),x=c[l]=new Array(f);r(h,u,m,y,x,d,t);for(var b=0,C=0,v,k;b<p;++b)if(v=m[b]){for(b>=C&&(C=b+1);!(k=y[C])&&++C<p;);v._next=k||null}}return o=new de(o,i),o._enter=s,o._exit=c,o}function H2(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function U2(){return new de(this._exit||this._groups.map(fp),this._parents)}function Y2(e,t,r){var i=this.enter(),n=this,a=this.exit();return typeof e=="function"?(i=e(i),i&&(i=i.selection())):i=i.append(e+""),t!=null&&(n=t(n),n&&(n=n.selection())),r==null?a.remove():r(a),i&&n?i.merge(n).order():n}function j2(e){for(var t=e.selection?e.selection():e,r=this._groups,i=t._groups,n=r.length,a=i.length,o=Math.min(n,a),s=new Array(n),c=0;c<o;++c)for(var l=r[c],h=i[c],u=l.length,f=s[c]=new Array(u),d,p=0;p<u;++p)(d=l[p]||h[p])&&(f[p]=d);for(;c<n;++c)s[c]=r[c];return new de(s,this._parents)}function G2(){for(var e=this._groups,t=-1,r=e.length;++t<r;)for(var i=e[t],n=i.length-1,a=i[n],o;--n>=0;)(o=i[n])&&(a&&o.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(o,a),a=o);return this}function V2(e){e||(e=X2);function t(u,f){return u&&f?e(u.__data__,f.__data__):!u-!f}for(var r=this._groups,i=r.length,n=new Array(i),a=0;a<i;++a){for(var o=r[a],s=o.length,c=n[a]=new Array(s),l,h=0;h<s;++h)(l=o[h])&&(c[h]=l);c.sort(t)}return new de(n,this._parents).order()}function X2(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Z2(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function K2(){return Array.from(this)}function Q2(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],n=0,a=i.length;n<a;++n){var o=i[n];if(o)return o}return null}function J2(){let e=0;for(const t of this)++e;return e}function t_(){return!this.node()}function e_(e){for(var t=this._groups,r=0,i=t.length;r<i;++r)for(var n=t[r],a=0,o=n.length,s;a<o;++a)(s=n[a])&&e.call(s,s.__data__,a,n);return this}function r_(e){return function(){this.removeAttribute(e)}}function i_(e){return function(){this.removeAttributeNS(e.space,e.local)}}function n_(e,t){return function(){this.setAttribute(e,t)}}function a_(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function s_(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttribute(e):this.setAttribute(e,r)}}function o_(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,r)}}function l_(e,t){var r=bs(e);if(arguments.length<2){var i=this.node();return r.local?i.getAttributeNS(r.space,r.local):i.getAttribute(r)}return this.each((t==null?r.local?i_:r_:typeof t=="function"?r.local?o_:s_:r.local?a_:n_)(r,t))}function dp(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function c_(e){return function(){this.style.removeProperty(e)}}function h_(e,t,r){return function(){this.style.setProperty(e,t,r)}}function u_(e,t,r){return function(){var i=t.apply(this,arguments);i==null?this.style.removeProperty(e):this.style.setProperty(e,i,r)}}function f_(e,t,r){return arguments.length>1?this.each((t==null?c_:typeof t=="function"?u_:h_)(e,t,r??"")):Ci(this.node(),e)}function Ci(e,t){return e.style.getPropertyValue(t)||dp(e).getComputedStyle(e,null).getPropertyValue(t)}function d_(e){return function(){delete this[e]}}function p_(e,t){return function(){this[e]=t}}function g_(e,t){return function(){var r=t.apply(this,arguments);r==null?delete this[e]:this[e]=r}}function m_(e,t){return arguments.length>1?this.each((t==null?d_:typeof t=="function"?g_:p_)(e,t)):this.node()[e]}function pp(e){return e.trim().split(/^|\s+/)}function Ul(e){return e.classList||new gp(e)}function gp(e){this._node=e,this._names=pp(e.getAttribute("class")||"")}gp.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function mp(e,t){for(var r=Ul(e),i=-1,n=t.length;++i<n;)r.add(t[i])}function yp(e,t){for(var r=Ul(e),i=-1,n=t.length;++i<n;)r.remove(t[i])}function y_(e){return function(){mp(this,e)}}function x_(e){return function(){yp(this,e)}}function b_(e,t){return function(){(t.apply(this,arguments)?mp:yp)(this,e)}}function __(e,t){var r=pp(e+"");if(arguments.length<2){for(var i=Ul(this.node()),n=-1,a=r.length;++n<a;)if(!i.contains(r[n]))return!1;return!0}return this.each((typeof t=="function"?b_:t?y_:x_)(r,t))}function C_(){this.textContent=""}function w_(e){return function(){this.textContent=e}}function k_(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function v_(e){return arguments.length?this.each(e==null?C_:(typeof e=="function"?k_:w_)(e)):this.node().textContent}function S_(){this.innerHTML=""}function T_(e){return function(){this.innerHTML=e}}function M_(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function A_(e){return arguments.length?this.each(e==null?S_:(typeof e=="function"?M_:T_)(e)):this.node().innerHTML}function L_(){this.nextSibling&&this.parentNode.appendChild(this)}function B_(){return this.each(L_)}function E_(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function F_(){return this.each(E_)}function $_(e){var t=typeof e=="function"?e:lp(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function D_(){return null}function O_(e,t){var r=typeof e=="function"?e:lp(e),i=t==null?D_:typeof t=="function"?t:Hl(t);return this.select(function(){return this.insertBefore(r.apply(this,arguments),i.apply(this,arguments)||null)})}function R_(){var e=this.parentNode;e&&e.removeChild(this)}function I_(){return this.each(R_)}function P_(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function N_(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function z_(e){return this.select(e?N_:P_)}function W_(e){return arguments.length?this.property("__data__",e):this.node().__data__}function q_(e){return function(t){e.call(this,t,this.__data__)}}function H_(e){return e.trim().split(/^|\s+/).map(function(t){var r="",i=t.indexOf(".");return i>=0&&(r=t.slice(i+1),t=t.slice(0,i)),{type:t,name:r}})}function U_(e){return function(){var t=this.__on;if(t){for(var r=0,i=-1,n=t.length,a;r<n;++r)a=t[r],(!e.type||a.type===e.type)&&a.name===e.name?this.removeEventListener(a.type,a.listener,a.options):t[++i]=a;++i?t.length=i:delete this.__on}}}function Y_(e,t,r){return function(){var i=this.__on,n,a=q_(t);if(i){for(var o=0,s=i.length;o<s;++o)if((n=i[o]).type===e.type&&n.name===e.name){this.removeEventListener(n.type,n.listener,n.options),this.addEventListener(n.type,n.listener=a,n.options=r),n.value=t;return}}this.addEventListener(e.type,a,r),n={type:e.type,name:e.name,value:t,listener:a,options:r},i?i.push(n):this.__on=[n]}}function j_(e,t,r){var i=H_(e+""),n,a=i.length,o;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,l=s.length,h;c<l;++c)for(n=0,h=s[c];n<a;++n)if((o=i[n]).type===h.type&&o.name===h.name)return h.value}return}for(s=t?Y_:U_,n=0;n<a;++n)this.each(s(i[n],t,r));return this}function xp(e,t,r){var i=dp(e),n=i.CustomEvent;typeof n=="function"?n=new n(t,r):(n=i.document.createEvent("Event"),r?(n.initEvent(t,r.bubbles,r.cancelable),n.detail=r.detail):n.initEvent(t,!1,!1)),e.dispatchEvent(n)}function G_(e,t){return function(){return xp(this,e,t)}}function V_(e,t){return function(){return xp(this,e,t.apply(this,arguments))}}function X_(e,t){return this.each((typeof t=="function"?V_:G_)(e,t))}function*Z_(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],n=0,a=i.length,o;n<a;++n)(o=i[n])&&(yield o)}var bp=[null];function de(e,t){this._groups=e,this._parents=t}function Bn(){return new de([[document.documentElement]],bp)}function K_(){return this}de.prototype=Bn.prototype={constructor:de,select:k2,selectAll:M2,selectChild:E2,selectChildren:O2,filter:R2,data:q2,enter:I2,exit:U2,join:Y2,merge:j2,selection:K_,order:G2,sort:V2,call:Z2,nodes:K2,node:Q2,size:J2,empty:t_,each:e_,attr:l_,style:f_,property:m_,classed:__,text:v_,html:A_,raise:B_,lower:F_,append:$_,insert:O_,remove:I_,clone:z_,datum:W_,on:j_,dispatch:X_,[Symbol.iterator]:Z_};function pt(e){return typeof e=="string"?new de([[document.querySelector(e)]],[document.documentElement]):new de([[e]],bp)}function En(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function _s(e,t){var r=Object.create(e.prototype);for(var i in t)r[i]=t[i];return r}function Rr(){}var bn=.7,Oa=1/bn,ii="\\s*([+-]?\\d+)\\s*",_n="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Oe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Q_=/^#([0-9a-f]{3,8})$/,J_=new RegExp(`^rgb\\(${ii},${ii},${ii}\\)$`),tC=new RegExp(`^rgb\\(${Oe},${Oe},${Oe}\\)$`),eC=new RegExp(`^rgba\\(${ii},${ii},${ii},${_n}\\)$`),rC=new RegExp(`^rgba\\(${Oe},${Oe},${Oe},${_n}\\)$`),iC=new RegExp(`^hsl\\(${_n},${Oe},${Oe}\\)$`),nC=new RegExp(`^hsla\\(${_n},${Oe},${Oe},${_n}\\)$`),vh={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};En(Rr,Br,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Sh,formatHex:Sh,formatHex8:aC,formatHsl:sC,formatRgb:Th,toString:Th});function Sh(){return this.rgb().formatHex()}function aC(){return this.rgb().formatHex8()}function sC(){return Cp(this).formatHsl()}function Th(){return this.rgb().formatRgb()}function Br(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Q_.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Mh(t):r===3?new Vt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Gn(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Gn(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=J_.exec(e))?new Vt(t[1],t[2],t[3],1):(t=tC.exec(e))?new Vt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=eC.exec(e))?Gn(t[1],t[2],t[3],t[4]):(t=rC.exec(e))?Gn(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=iC.exec(e))?Bh(t[1],t[2]/100,t[3]/100,1):(t=nC.exec(e))?Bh(t[1],t[2]/100,t[3]/100,t[4]):vh.hasOwnProperty(e)?Mh(vh[e]):e==="transparent"?new Vt(NaN,NaN,NaN,0):null}function Mh(e){return new Vt(e>>16&255,e>>8&255,e&255,1)}function Gn(e,t,r,i){return i<=0&&(e=t=r=NaN),new Vt(e,t,r,i)}function _p(e){return e instanceof Rr||(e=Br(e)),e?(e=e.rgb(),new Vt(e.r,e.g,e.b,e.opacity)):new Vt}function Jo(e,t,r,i){return arguments.length===1?_p(e):new Vt(e,t,r,i??1)}function Vt(e,t,r,i){this.r=+e,this.g=+t,this.b=+r,this.opacity=+i}En(Vt,Jo,_s(Rr,{brighter(e){return e=e==null?Oa:Math.pow(Oa,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?bn:Math.pow(bn,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Vt(Tr(this.r),Tr(this.g),Tr(this.b),Ra(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ah,formatHex:Ah,formatHex8:oC,formatRgb:Lh,toString:Lh}));function Ah(){return`#${vr(this.r)}${vr(this.g)}${vr(this.b)}`}function oC(){return`#${vr(this.r)}${vr(this.g)}${vr(this.b)}${vr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Lh(){const e=Ra(this.opacity);return`${e===1?"rgb(":"rgba("}${Tr(this.r)}, ${Tr(this.g)}, ${Tr(this.b)}${e===1?")":`, ${e})`}`}function Ra(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Tr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function vr(e){return e=Tr(e),(e<16?"0":"")+e.toString(16)}function Bh(e,t,r,i){return i<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ke(e,t,r,i)}function Cp(e){if(e instanceof ke)return new ke(e.h,e.s,e.l,e.opacity);if(e instanceof Rr||(e=Br(e)),!e)return new ke;if(e instanceof ke)return e;e=e.rgb();var t=e.r/255,r=e.g/255,i=e.b/255,n=Math.min(t,r,i),a=Math.max(t,r,i),o=NaN,s=a-n,c=(a+n)/2;return s?(t===a?o=(r-i)/s+(r<i)*6:r===a?o=(i-t)/s+2:o=(t-r)/s+4,s/=c<.5?a+n:2-a-n,o*=60):s=c>0&&c<1?0:o,new ke(o,s,c,e.opacity)}function lC(e,t,r,i){return arguments.length===1?Cp(e):new ke(e,t,r,i??1)}function ke(e,t,r,i){this.h=+e,this.s=+t,this.l=+r,this.opacity=+i}En(ke,lC,_s(Rr,{brighter(e){return e=e==null?Oa:Math.pow(Oa,e),new ke(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?bn:Math.pow(bn,e),new ke(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,i=r+(r<.5?r:1-r)*t,n=2*r-i;return new Vt(io(e>=240?e-240:e+120,n,i),io(e,n,i),io(e<120?e+240:e-120,n,i),this.opacity)},clamp(){return new ke(Eh(this.h),Vn(this.s),Vn(this.l),Ra(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ra(this.opacity);return`${e===1?"hsl(":"hsla("}${Eh(this.h)}, ${Vn(this.s)*100}%, ${Vn(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Eh(e){return e=(e||0)%360,e<0?e+360:e}function Vn(e){return Math.max(0,Math.min(1,e||0))}function io(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const cC=Math.PI/180,hC=180/Math.PI,Ia=18,wp=.96422,kp=1,vp=.82521,Sp=4/29,ni=6/29,Tp=3*ni*ni,uC=ni*ni*ni;function Mp(e){if(e instanceof Re)return new Re(e.l,e.a,e.b,e.opacity);if(e instanceof Ge)return Ap(e);e instanceof Vt||(e=_p(e));var t=oo(e.r),r=oo(e.g),i=oo(e.b),n=no((.2225045*t+.7168786*r+.0606169*i)/kp),a,o;return t===r&&r===i?a=o=n:(a=no((.4360747*t+.3850649*r+.1430804*i)/wp),o=no((.0139322*t+.0971045*r+.7141733*i)/vp)),new Re(116*n-16,500*(a-n),200*(n-o),e.opacity)}function fC(e,t,r,i){return arguments.length===1?Mp(e):new Re(e,t,r,i??1)}function Re(e,t,r,i){this.l=+e,this.a=+t,this.b=+r,this.opacity=+i}En(Re,fC,_s(Rr,{brighter(e){return new Re(this.l+Ia*(e??1),this.a,this.b,this.opacity)},darker(e){return new Re(this.l-Ia*(e??1),this.a,this.b,this.opacity)},rgb(){var e=(this.l+16)/116,t=isNaN(this.a)?e:e+this.a/500,r=isNaN(this.b)?e:e-this.b/200;return t=wp*ao(t),e=kp*ao(e),r=vp*ao(r),new Vt(so(3.1338561*t-1.6168667*e-.4906146*r),so(-.9787684*t+1.9161415*e+.033454*r),so(.0719453*t-.2289914*e+1.4052427*r),this.opacity)}}));function no(e){return e>uC?Math.pow(e,1/3):e/Tp+Sp}function ao(e){return e>ni?e*e*e:Tp*(e-Sp)}function so(e){return 255*(e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)}function oo(e){return(e/=255)<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}function dC(e){if(e instanceof Ge)return new Ge(e.h,e.c,e.l,e.opacity);if(e instanceof Re||(e=Mp(e)),e.a===0&&e.b===0)return new Ge(NaN,0<e.l&&e.l<100?0:NaN,e.l,e.opacity);var t=Math.atan2(e.b,e.a)*hC;return new Ge(t<0?t+360:t,Math.sqrt(e.a*e.a+e.b*e.b),e.l,e.opacity)}function tl(e,t,r,i){return arguments.length===1?dC(e):new Ge(e,t,r,i??1)}function Ge(e,t,r,i){this.h=+e,this.c=+t,this.l=+r,this.opacity=+i}function Ap(e){if(isNaN(e.h))return new Re(e.l,0,0,e.opacity);var t=e.h*cC;return new Re(e.l,Math.cos(t)*e.c,Math.sin(t)*e.c,e.opacity)}En(Ge,tl,_s(Rr,{brighter(e){return new Ge(this.h,this.c,this.l+Ia*(e??1),this.opacity)},darker(e){return new Ge(this.h,this.c,this.l-Ia*(e??1),this.opacity)},rgb(){return Ap(this).rgb()}}));const Cs=e=>()=>e;function Lp(e,t){return function(r){return e+r*t}}function pC(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(i){return Math.pow(e+i*t,r)}}function gC(e,t){var r=t-e;return r?Lp(e,r>180||r<-180?r-360*Math.round(r/360):r):Cs(isNaN(e)?t:e)}function mC(e){return(e=+e)==1?un:function(t,r){return r-t?pC(t,r,e):Cs(isNaN(t)?r:t)}}function un(e,t){var r=t-e;return r?Lp(e,r):Cs(isNaN(e)?t:e)}const Pa=function e(t){var r=mC(t);function i(n,a){var o=r((n=Jo(n)).r,(a=Jo(a)).r),s=r(n.g,a.g),c=r(n.b,a.b),l=un(n.opacity,a.opacity);return function(h){return n.r=o(h),n.g=s(h),n.b=c(h),n.opacity=l(h),n+""}}return i.gamma=e,i}(1);function yC(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,i=t.slice(),n;return function(a){for(n=0;n<r;++n)i[n]=e[n]*(1-a)+t[n]*a;return i}}function xC(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function bC(e,t){var r=t?t.length:0,i=e?Math.min(r,e.length):0,n=new Array(i),a=new Array(r),o;for(o=0;o<i;++o)n[o]=Yl(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(s){for(o=0;o<i;++o)a[o]=n[o](s);return a}}function _C(e,t){var r=new Date;return e=+e,t=+t,function(i){return r.setTime(e*(1-i)+t*i),r}}function we(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function CC(e,t){var r={},i={},n;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(n in t)n in e?r[n]=Yl(e[n],t[n]):i[n]=t[n];return function(a){for(n in r)i[n]=r[n](a);return i}}var el=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,lo=new RegExp(el.source,"g");function wC(e){return function(){return e}}function kC(e){return function(t){return e(t)+""}}function Bp(e,t){var r=el.lastIndex=lo.lastIndex=0,i,n,a,o=-1,s=[],c=[];for(e=e+"",t=t+"";(i=el.exec(e))&&(n=lo.exec(t));)(a=n.index)>r&&(a=t.slice(r,a),s[o]?s[o]+=a:s[++o]=a),(i=i[0])===(n=n[0])?s[o]?s[o]+=n:s[++o]=n:(s[++o]=null,c.push({i:o,x:we(i,n)})),r=lo.lastIndex;return r<t.length&&(a=t.slice(r),s[o]?s[o]+=a:s[++o]=a),s.length<2?c[0]?kC(c[0].x):wC(t):(t=c.length,function(l){for(var h=0,u;h<t;++h)s[(u=c[h]).i]=u.x(l);return s.join("")})}function Yl(e,t){var r=typeof t,i;return t==null||r==="boolean"?Cs(t):(r==="number"?we:r==="string"?(i=Br(t))?(t=i,Pa):Bp:t instanceof Br?Pa:t instanceof Date?_C:xC(t)?yC:Array.isArray(t)?bC:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?CC:we)(e,t)}function vC(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}var Fh=180/Math.PI,rl={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Ep(e,t,r,i,n,a){var o,s,c;return(o=Math.sqrt(e*e+t*t))&&(e/=o,t/=o),(c=e*r+t*i)&&(r-=e*c,i-=t*c),(s=Math.sqrt(r*r+i*i))&&(r/=s,i/=s,c/=s),e*i<t*r&&(e=-e,t=-t,c=-c,o=-o),{translateX:n,translateY:a,rotate:Math.atan2(t,e)*Fh,skewX:Math.atan(c)*Fh,scaleX:o,scaleY:s}}var Xn;function SC(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?rl:Ep(t.a,t.b,t.c,t.d,t.e,t.f)}function TC(e){return e==null||(Xn||(Xn=document.createElementNS("http://www.w3.org/2000/svg","g")),Xn.setAttribute("transform",e),!(e=Xn.transform.baseVal.consolidate()))?rl:(e=e.matrix,Ep(e.a,e.b,e.c,e.d,e.e,e.f))}function Fp(e,t,r,i){function n(l){return l.length?l.pop()+" ":""}function a(l,h,u,f,d,p){if(l!==u||h!==f){var m=d.push("translate(",null,t,null,r);p.push({i:m-4,x:we(l,u)},{i:m-2,x:we(h,f)})}else(u||f)&&d.push("translate("+u+t+f+r)}function o(l,h,u,f){l!==h?(l-h>180?h+=360:h-l>180&&(l+=360),f.push({i:u.push(n(u)+"rotate(",null,i)-2,x:we(l,h)})):h&&u.push(n(u)+"rotate("+h+i)}function s(l,h,u,f){l!==h?f.push({i:u.push(n(u)+"skewX(",null,i)-2,x:we(l,h)}):h&&u.push(n(u)+"skewX("+h+i)}function c(l,h,u,f,d,p){if(l!==u||h!==f){var m=d.push(n(d)+"scale(",null,",",null,")");p.push({i:m-4,x:we(l,u)},{i:m-2,x:we(h,f)})}else(u!==1||f!==1)&&d.push(n(d)+"scale("+u+","+f+")")}return function(l,h){var u=[],f=[];return l=e(l),h=e(h),a(l.translateX,l.translateY,h.translateX,h.translateY,u,f),o(l.rotate,h.rotate,u,f),s(l.skewX,h.skewX,u,f),c(l.scaleX,l.scaleY,h.scaleX,h.scaleY,u,f),l=h=null,function(d){for(var p=-1,m=f.length,y;++p<m;)u[(y=f[p]).i]=y.x(d);return u.join("")}}}var MC=Fp(SC,"px, ","px)","deg)"),AC=Fp(TC,", ",")",")");function LC(e){return function(t,r){var i=e((t=tl(t)).h,(r=tl(r)).h),n=un(t.c,r.c),a=un(t.l,r.l),o=un(t.opacity,r.opacity);return function(s){return t.h=i(s),t.c=n(s),t.l=a(s),t.opacity=o(s),t+""}}}const d3=LC(gC);var wi=0,en=0,Hi=0,$p=1e3,Na,rn,za=0,Er=0,ws=0,Cn=typeof performance=="object"&&performance.now?performance:Date,Dp=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function jl(){return Er||(Dp(BC),Er=Cn.now()+ws)}function BC(){Er=0}function Wa(){this._call=this._time=this._next=null}Wa.prototype=Op.prototype={constructor:Wa,restart:function(e,t,r){if(typeof e!="function")throw new TypeError("callback is not a function");r=(r==null?jl():+r)+(t==null?0:+t),!this._next&&rn!==this&&(rn?rn._next=this:Na=this,rn=this),this._call=e,this._time=r,il()},stop:function(){this._call&&(this._call=null,this._time=1/0,il())}};function Op(e,t,r){var i=new Wa;return i.restart(e,t,r),i}function EC(){jl(),++wi;for(var e=Na,t;e;)(t=Er-e._time)>=0&&e._call.call(void 0,t),e=e._next;--wi}function $h(){Er=(za=Cn.now())+ws,wi=en=0;try{EC()}finally{wi=0,$C(),Er=0}}function FC(){var e=Cn.now(),t=e-za;t>$p&&(ws-=t,za=e)}function $C(){for(var e,t=Na,r,i=1/0;t;)t._call?(i>t._time&&(i=t._time),e=t,t=t._next):(r=t._next,t._next=null,t=e?e._next=r:Na=r);rn=e,il(i)}function il(e){if(!wi){en&&(en=clearTimeout(en));var t=e-Er;t>24?(e<1/0&&(en=setTimeout($h,e-Cn.now()-ws)),Hi&&(Hi=clearInterval(Hi))):(Hi||(za=Cn.now(),Hi=setInterval(FC,$p)),wi=1,Dp($h))}}function Dh(e,t,r){var i=new Wa;return t=t==null?0:+t,i.restart(n=>{i.stop(),e(n+t)},t,r),i}var DC=op("start","end","cancel","interrupt"),OC=[],Rp=0,Oh=1,nl=2,da=3,Rh=4,al=5,pa=6;function ks(e,t,r,i,n,a){var o=e.__transition;if(!o)e.__transition={};else if(r in o)return;RC(e,r,{name:t,index:i,group:n,on:DC,tween:OC,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:Rp})}function Gl(e,t){var r=Me(e,t);if(r.state>Rp)throw new Error("too late; already scheduled");return r}function Pe(e,t){var r=Me(e,t);if(r.state>da)throw new Error("too late; already running");return r}function Me(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function RC(e,t,r){var i=e.__transition,n;i[t]=r,r.timer=Op(a,0,r.time);function a(l){r.state=Oh,r.timer.restart(o,r.delay,r.time),r.delay<=l&&o(l-r.delay)}function o(l){var h,u,f,d;if(r.state!==Oh)return c();for(h in i)if(d=i[h],d.name===r.name){if(d.state===da)return Dh(o);d.state===Rh?(d.state=pa,d.timer.stop(),d.on.call("interrupt",e,e.__data__,d.index,d.group),delete i[h]):+h<t&&(d.state=pa,d.timer.stop(),d.on.call("cancel",e,e.__data__,d.index,d.group),delete i[h])}if(Dh(function(){r.state===da&&(r.state=Rh,r.timer.restart(s,r.delay,r.time),s(l))}),r.state=nl,r.on.call("start",e,e.__data__,r.index,r.group),r.state===nl){for(r.state=da,n=new Array(f=r.tween.length),h=0,u=-1;h<f;++h)(d=r.tween[h].value.call(e,e.__data__,r.index,r.group))&&(n[++u]=d);n.length=u+1}}function s(l){for(var h=l<r.duration?r.ease.call(null,l/r.duration):(r.timer.restart(c),r.state=al,1),u=-1,f=n.length;++u<f;)n[u].call(e,h);r.state===al&&(r.on.call("end",e,e.__data__,r.index,r.group),c())}function c(){r.state=pa,r.timer.stop(),delete i[t];for(var l in i)return;delete e.__transition}}function IC(e,t){var r=e.__transition,i,n,a=!0,o;if(r){t=t==null?null:t+"";for(o in r){if((i=r[o]).name!==t){a=!1;continue}n=i.state>nl&&i.state<al,i.state=pa,i.timer.stop(),i.on.call(n?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[o]}a&&delete e.__transition}}function PC(e){return this.each(function(){IC(this,e)})}function NC(e,t){var r,i;return function(){var n=Pe(this,e),a=n.tween;if(a!==r){i=r=a;for(var o=0,s=i.length;o<s;++o)if(i[o].name===t){i=i.slice(),i.splice(o,1);break}}n.tween=i}}function zC(e,t,r){var i,n;if(typeof r!="function")throw new Error;return function(){var a=Pe(this,e),o=a.tween;if(o!==i){n=(i=o).slice();for(var s={name:t,value:r},c=0,l=n.length;c<l;++c)if(n[c].name===t){n[c]=s;break}c===l&&n.push(s)}a.tween=n}}function WC(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=Me(this.node(),r).tween,n=0,a=i.length,o;n<a;++n)if((o=i[n]).name===e)return o.value;return null}return this.each((t==null?NC:zC)(r,e,t))}function Vl(e,t,r){var i=e._id;return e.each(function(){var n=Pe(this,i);(n.value||(n.value={}))[t]=r.apply(this,arguments)}),function(n){return Me(n,i).value[t]}}function Ip(e,t){var r;return(typeof t=="number"?we:t instanceof Br?Pa:(r=Br(t))?(t=r,Pa):Bp)(e,t)}function qC(e){return function(){this.removeAttribute(e)}}function HC(e){return function(){this.removeAttributeNS(e.space,e.local)}}function UC(e,t,r){var i,n=r+"",a;return function(){var o=this.getAttribute(e);return o===n?null:o===i?a:a=t(i=o,r)}}function YC(e,t,r){var i,n=r+"",a;return function(){var o=this.getAttributeNS(e.space,e.local);return o===n?null:o===i?a:a=t(i=o,r)}}function jC(e,t,r){var i,n,a;return function(){var o,s=r(this),c;return s==null?void this.removeAttribute(e):(o=this.getAttribute(e),c=s+"",o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s)))}}function GC(e,t,r){var i,n,a;return function(){var o,s=r(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),c=s+"",o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s)))}}function VC(e,t){var r=bs(e),i=r==="transform"?AC:Ip;return this.attrTween(e,typeof t=="function"?(r.local?GC:jC)(r,i,Vl(this,"attr."+e,t)):t==null?(r.local?HC:qC)(r):(r.local?YC:UC)(r,i,t))}function XC(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function ZC(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function KC(e,t){var r,i;function n(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&ZC(e,a)),r}return n._value=t,n}function QC(e,t){var r,i;function n(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&XC(e,a)),r}return n._value=t,n}function JC(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=bs(e);return this.tween(r,(i.local?KC:QC)(i,t))}function tw(e,t){return function(){Gl(this,e).delay=+t.apply(this,arguments)}}function ew(e,t){return t=+t,function(){Gl(this,e).delay=t}}function rw(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?tw:ew)(t,e)):Me(this.node(),t).delay}function iw(e,t){return function(){Pe(this,e).duration=+t.apply(this,arguments)}}function nw(e,t){return t=+t,function(){Pe(this,e).duration=t}}function aw(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?iw:nw)(t,e)):Me(this.node(),t).duration}function sw(e,t){if(typeof t!="function")throw new Error;return function(){Pe(this,e).ease=t}}function ow(e){var t=this._id;return arguments.length?this.each(sw(t,e)):Me(this.node(),t).ease}function lw(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;Pe(this,e).ease=r}}function cw(e){if(typeof e!="function")throw new Error;return this.each(lw(this._id,e))}function hw(e){typeof e!="function"&&(e=hp(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=[],c,l=0;l<o;++l)(c=a[l])&&e.call(c,c.__data__,l,a)&&s.push(c);return new Qe(i,this._parents,this._name,this._id)}function uw(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,n=r.length,a=Math.min(i,n),o=new Array(i),s=0;s<a;++s)for(var c=t[s],l=r[s],h=c.length,u=o[s]=new Array(h),f,d=0;d<h;++d)(f=c[d]||l[d])&&(u[d]=f);for(;s<i;++s)o[s]=t[s];return new Qe(o,this._parents,this._name,this._id)}function fw(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function dw(e,t,r){var i,n,a=fw(t)?Gl:Pe;return function(){var o=a(this,e),s=o.on;s!==i&&(n=(i=s).copy()).on(t,r),o.on=n}}function pw(e,t){var r=this._id;return arguments.length<2?Me(this.node(),r).on.on(e):this.each(dw(r,e,t))}function gw(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function mw(){return this.on("end.remove",gw(this._id))}function yw(e){var t=this._name,r=this._id;typeof e!="function"&&(e=Hl(e));for(var i=this._groups,n=i.length,a=new Array(n),o=0;o<n;++o)for(var s=i[o],c=s.length,l=a[o]=new Array(c),h,u,f=0;f<c;++f)(h=s[f])&&(u=e.call(h,h.__data__,f,s))&&("__data__"in h&&(u.__data__=h.__data__),l[f]=u,ks(l[f],t,r,f,l,Me(h,r)));return new Qe(a,this._parents,t,r)}function xw(e){var t=this._name,r=this._id;typeof e!="function"&&(e=cp(e));for(var i=this._groups,n=i.length,a=[],o=[],s=0;s<n;++s)for(var c=i[s],l=c.length,h,u=0;u<l;++u)if(h=c[u]){for(var f=e.call(h,h.__data__,u,c),d,p=Me(h,r),m=0,y=f.length;m<y;++m)(d=f[m])&&ks(d,t,r,m,f,p);a.push(f),o.push(h)}return new Qe(a,o,t,r)}var bw=Bn.prototype.constructor;function _w(){return new bw(this._groups,this._parents)}function Cw(e,t){var r,i,n;return function(){var a=Ci(this,e),o=(this.style.removeProperty(e),Ci(this,e));return a===o?null:a===r&&o===i?n:n=t(r=a,i=o)}}function Pp(e){return function(){this.style.removeProperty(e)}}function ww(e,t,r){var i,n=r+"",a;return function(){var o=Ci(this,e);return o===n?null:o===i?a:a=t(i=o,r)}}function kw(e,t,r){var i,n,a;return function(){var o=Ci(this,e),s=r(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),Ci(this,e))),o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s))}}function vw(e,t){var r,i,n,a="style."+t,o="end."+a,s;return function(){var c=Pe(this,e),l=c.on,h=c.value[a]==null?s||(s=Pp(t)):void 0;(l!==r||n!==h)&&(i=(r=l).copy()).on(o,n=h),c.on=i}}function Sw(e,t,r){var i=(e+="")=="transform"?MC:Ip;return t==null?this.styleTween(e,Cw(e,i)).on("end.style."+e,Pp(e)):typeof t=="function"?this.styleTween(e,kw(e,i,Vl(this,"style."+e,t))).each(vw(this._id,e)):this.styleTween(e,ww(e,i,t),r).on("end.style."+e,null)}function Tw(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function Mw(e,t,r){var i,n;function a(){var o=t.apply(this,arguments);return o!==n&&(i=(n=o)&&Tw(e,o,r)),i}return a._value=t,a}function Aw(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,Mw(e,t,r??""))}function Lw(e){return function(){this.textContent=e}}function Bw(e){return function(){var t=e(this);this.textContent=t??""}}function Ew(e){return this.tween("text",typeof e=="function"?Bw(Vl(this,"text",e)):Lw(e==null?"":e+""))}function Fw(e){return function(t){this.textContent=e.call(this,t)}}function $w(e){var t,r;function i(){var n=e.apply(this,arguments);return n!==r&&(t=(r=n)&&Fw(n)),t}return i._value=e,i}function Dw(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,$w(e))}function Ow(){for(var e=this._name,t=this._id,r=Np(),i=this._groups,n=i.length,a=0;a<n;++a)for(var o=i[a],s=o.length,c,l=0;l<s;++l)if(c=o[l]){var h=Me(c,t);ks(c,e,r,l,o,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new Qe(i,this._parents,e,r)}function Rw(){var e,t,r=this,i=r._id,n=r.size();return new Promise(function(a,o){var s={value:o},c={value:function(){--n===0&&a()}};r.each(function(){var l=Pe(this,i),h=l.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),l.on=t}),n===0&&a()})}var Iw=0;function Qe(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function Np(){return++Iw}var Ue=Bn.prototype;Qe.prototype={constructor:Qe,select:yw,selectAll:xw,selectChild:Ue.selectChild,selectChildren:Ue.selectChildren,filter:hw,merge:uw,selection:_w,transition:Ow,call:Ue.call,nodes:Ue.nodes,node:Ue.node,size:Ue.size,empty:Ue.empty,each:Ue.each,on:pw,attr:VC,attrTween:JC,style:Sw,styleTween:Aw,text:Ew,textTween:Dw,remove:mw,tween:WC,delay:rw,duration:aw,ease:ow,easeVarying:cw,end:Rw,[Symbol.iterator]:Ue[Symbol.iterator]};function Pw(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Nw={time:null,delay:0,duration:250,ease:Pw};function zw(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function Ww(e){var t,r;e instanceof Qe?(t=e._id,e=e._name):(t=Np(),(r=Nw).time=jl(),e=e==null?null:e+"");for(var i=this._groups,n=i.length,a=0;a<n;++a)for(var o=i[a],s=o.length,c,l=0;l<s;++l)(c=o[l])&&ks(c,e,t,l,o,r||zw(c,t));return new Qe(i,this._parents,e,t)}Bn.prototype.interrupt=PC;Bn.prototype.transition=Ww;const sl=Math.PI,ol=2*sl,xr=1e-6,qw=ol-xr;function zp(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function Hw(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return zp;const r=10**t;return function(i){this._+=i[0];for(let n=1,a=i.length;n<a;++n)this._+=Math.round(arguments[n]*r)/r+i[n]}}class Uw{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?zp:Hw(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,i,n){this._append`Q${+t},${+r},${this._x1=+i},${this._y1=+n}`}bezierCurveTo(t,r,i,n,a,o){this._append`C${+t},${+r},${+i},${+n},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,i,n,a){if(t=+t,r=+r,i=+i,n=+n,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,c=i-t,l=n-r,h=o-t,u=s-r,f=h*h+u*u;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(f>xr)if(!(Math.abs(u*c-l*h)>xr)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=i-o,p=n-s,m=c*c+l*l,y=d*d+p*p,x=Math.sqrt(m),b=Math.sqrt(f),C=a*Math.tan((sl-Math.acos((m+f-y)/(2*x*b)))/2),v=C/b,k=C/x;Math.abs(v-1)>xr&&this._append`L${t+v*h},${r+v*u}`,this._append`A${a},${a},0,0,${+(u*d>h*p)},${this._x1=t+k*c},${this._y1=r+k*l}`}}arc(t,r,i,n,a,o){if(t=+t,r=+r,i=+i,o=!!o,i<0)throw new Error(`negative radius: ${i}`);let s=i*Math.cos(n),c=i*Math.sin(n),l=t+s,h=r+c,u=1^o,f=o?n-a:a-n;this._x1===null?this._append`M${l},${h}`:(Math.abs(this._x1-l)>xr||Math.abs(this._y1-h)>xr)&&this._append`L${l},${h}`,i&&(f<0&&(f=f%ol+ol),f>qw?this._append`A${i},${i},0,1,${u},${t-s},${r-c}A${i},${i},0,1,${u},${this._x1=l},${this._y1=h}`:f>xr&&this._append`A${i},${i},0,${+(f>=sl)},${u},${this._x1=t+i*Math.cos(a)},${this._y1=r+i*Math.sin(a)}`)}rect(t,r,i,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${i=+i}v${+n}h${-i}Z`}toString(){return this._}}function Yw(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function qa(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,i=e.slice(0,r);return[i.length>1?i[0]+i.slice(2):i,+e.slice(r+1)]}function ki(e){return e=qa(Math.abs(e)),e?e[1]:NaN}function jw(e,t){return function(r,i){for(var n=r.length,a=[],o=0,s=e[0],c=0;n>0&&s>0&&(c+s+1>i&&(s=Math.max(1,i-c)),a.push(r.substring(n-=s,n+s)),!((c+=s+1)>i));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function Gw(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var Vw=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Ha(e){if(!(t=Vw.exec(e)))throw new Error("invalid format: "+e);var t;return new Xl({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Ha.prototype=Xl.prototype;function Xl(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Xl.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Xw(e){t:for(var t=e.length,r=1,i=-1,n;r<t;++r)switch(e[r]){case".":i=n=r;break;case"0":i===0&&(i=r),n=r;break;default:if(!+e[r])break t;i>0&&(i=0);break}return i>0?e.slice(0,i)+e.slice(n+1):e}var Wp;function Zw(e,t){var r=qa(e,t);if(!r)return e+"";var i=r[0],n=r[1],a=n-(Wp=Math.max(-8,Math.min(8,Math.floor(n/3)))*3)+1,o=i.length;return a===o?i:a>o?i+new Array(a-o+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+new Array(1-a).join("0")+qa(e,Math.max(0,t+a-1))[0]}function Ih(e,t){var r=qa(e,t);if(!r)return e+"";var i=r[0],n=r[1];return n<0?"0."+new Array(-n).join("0")+i:i.length>n+1?i.slice(0,n+1)+"."+i.slice(n+1):i+new Array(n-i.length+2).join("0")}const Ph={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Yw,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ih(e*100,t),r:Ih,s:Zw,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Nh(e){return e}var zh=Array.prototype.map,Wh=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Kw(e){var t=e.grouping===void 0||e.thousands===void 0?Nh:jw(zh.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",i=e.currency===void 0?"":e.currency[1]+"",n=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Nh:Gw(zh.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function l(u){u=Ha(u);var f=u.fill,d=u.align,p=u.sign,m=u.symbol,y=u.zero,x=u.width,b=u.comma,C=u.precision,v=u.trim,k=u.type;k==="n"?(b=!0,k="g"):Ph[k]||(C===void 0&&(C=12),v=!0,k="g"),(y||f==="0"&&d==="=")&&(y=!0,f="0",d="=");var _=m==="$"?r:m==="#"&&/[boxX]/.test(k)?"0"+k.toLowerCase():"",w=m==="$"?i:/[%p]/.test(k)?o:"",O=Ph[k],N=/[defgprs%]/.test(k);C=C===void 0?6:/[gprs]/.test(k)?Math.max(1,Math.min(21,C)):Math.max(0,Math.min(20,C));function $(S){var I=_,E=w,A,F,L;if(k==="c")E=O(S)+E,S="";else{S=+S;var D=S<0||1/S<0;if(S=isNaN(S)?c:O(Math.abs(S),C),v&&(S=Xw(S)),D&&+S==0&&p!=="+"&&(D=!1),I=(D?p==="("?p:s:p==="-"||p==="("?"":p)+I,E=(k==="s"?Wh[8+Wp/3]:"")+E+(D&&p==="("?")":""),N){for(A=-1,F=S.length;++A<F;)if(L=S.charCodeAt(A),48>L||L>57){E=(L===46?n+S.slice(A+1):S.slice(A))+E,S=S.slice(0,A);break}}}b&&!y&&(S=t(S,1/0));var B=I.length+S.length+E.length,W=B<x?new Array(x-B+1).join(f):"";switch(b&&y&&(S=t(W+S,W.length?x-E.length:1/0),W=""),d){case"<":S=I+S+E+W;break;case"=":S=I+W+S+E;break;case"^":S=W.slice(0,B=W.length>>1)+I+S+E+W.slice(B);break;default:S=W+I+S+E;break}return a(S)}return $.toString=function(){return u+""},$}function h(u,f){var d=l((u=Ha(u),u.type="f",u)),p=Math.max(-8,Math.min(8,Math.floor(ki(f)/3)))*3,m=Math.pow(10,-p),y=Wh[8+p/3];return function(x){return d(m*x)+y}}return{format:l,formatPrefix:h}}var Zn,qp,Hp;Qw({thousands:",",grouping:[3],currency:["$",""]});function Qw(e){return Zn=Kw(e),qp=Zn.format,Hp=Zn.formatPrefix,Zn}function Jw(e){return Math.max(0,-ki(Math.abs(e)))}function tk(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(ki(t)/3)))*3-ki(Math.abs(e)))}function ek(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,ki(t)-ki(e))+1}function vs(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}const qh=Symbol("implicit");function Up(){var e=new bh,t=[],r=[],i=qh;function n(a){let o=e.get(a);if(o===void 0){if(i!==qh)return i;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return n.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new bh;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return n},n.range=function(a){return arguments.length?(r=Array.from(a),n):r.slice()},n.unknown=function(a){return arguments.length?(i=a,n):i},n.copy=function(){return Up(t,r).unknown(i)},vs.apply(n,arguments),n}function rk(){var e=Up().unknown(void 0),t=e.domain,r=e.range,i=0,n=1,a,o,s=!1,c=0,l=0,h=.5;delete e.unknown;function u(){var f=t().length,d=n<i,p=d?n:i,m=d?i:n;a=(m-p)/Math.max(1,f-c+l*2),s&&(a=Math.floor(a)),p+=(m-p-a*(f-c))*h,o=a*(1-c),s&&(p=Math.round(p),o=Math.round(o));var y=h2(f).map(function(x){return p+a*x});return r(d?y.reverse():y)}return e.domain=function(f){return arguments.length?(t(f),u()):t()},e.range=function(f){return arguments.length?([i,n]=f,i=+i,n=+n,u()):[i,n]},e.rangeRound=function(f){return[i,n]=f,i=+i,n=+n,s=!0,u()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(f){return arguments.length?(s=!!f,u()):s},e.padding=function(f){return arguments.length?(c=Math.min(1,l=+f),u()):c},e.paddingInner=function(f){return arguments.length?(c=Math.min(1,f),u()):c},e.paddingOuter=function(f){return arguments.length?(l=+f,u()):l},e.align=function(f){return arguments.length?(h=Math.max(0,Math.min(1,f)),u()):h},e.copy=function(){return rk(t(),[i,n]).round(s).paddingInner(c).paddingOuter(l).align(h)},vs.apply(u(),arguments)}function ik(e){return function(){return e}}function nk(e){return+e}var Hh=[0,1];function Jr(e){return e}function ll(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:ik(isNaN(t)?NaN:.5)}function ak(e,t){var r;return e>t&&(r=e,e=t,t=r),function(i){return Math.max(e,Math.min(t,i))}}function sk(e,t,r){var i=e[0],n=e[1],a=t[0],o=t[1];return n<i?(i=ll(n,i),a=r(o,a)):(i=ll(i,n),a=r(a,o)),function(s){return a(i(s))}}function ok(e,t,r){var i=Math.min(e.length,t.length)-1,n=new Array(i),a=new Array(i),o=-1;for(e[i]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<i;)n[o]=ll(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(s){var c=r2(e,s,1,i)-1;return a[c](n[c](s))}}function Yp(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function lk(){var e=Hh,t=Hh,r=Yl,i,n,a,o=Jr,s,c,l;function h(){var f=Math.min(e.length,t.length);return o!==Jr&&(o=ak(e[0],e[f-1])),s=f>2?ok:sk,c=l=null,u}function u(f){return f==null||isNaN(f=+f)?a:(c||(c=s(e.map(i),t,r)))(i(o(f)))}return u.invert=function(f){return o(n((l||(l=s(t,e.map(i),we)))(f)))},u.domain=function(f){return arguments.length?(e=Array.from(f,nk),h()):e.slice()},u.range=function(f){return arguments.length?(t=Array.from(f),h()):t.slice()},u.rangeRound=function(f){return t=Array.from(f),r=vC,h()},u.clamp=function(f){return arguments.length?(o=f?!0:Jr,h()):o!==Jr},u.interpolate=function(f){return arguments.length?(r=f,h()):r},u.unknown=function(f){return arguments.length?(a=f,u):a},function(f,d){return i=f,n=d,h()}}function jp(){return lk()(Jr,Jr)}function ck(e,t,r,i){var n=Zo(e,t,r),a;switch(i=Ha(i??",f"),i.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return i.precision==null&&!isNaN(a=tk(n,o))&&(i.precision=a),Hp(i,o)}case"":case"e":case"g":case"p":case"r":{i.precision==null&&!isNaN(a=ek(n,Math.max(Math.abs(e),Math.abs(t))))&&(i.precision=a-(i.type==="e"));break}case"f":case"%":{i.precision==null&&!isNaN(a=Jw(n))&&(i.precision=a-(i.type==="%")*2);break}}return qp(i)}function hk(e){var t=e.domain;return e.ticks=function(r){var i=t();return c2(i[0],i[i.length-1],r??10)},e.tickFormat=function(r,i){var n=t();return ck(n[0],n[n.length-1],r??10,i)},e.nice=function(r){r==null&&(r=10);var i=t(),n=0,a=i.length-1,o=i[n],s=i[a],c,l,h=10;for(s<o&&(l=o,o=s,s=l,l=n,n=a,a=l);h-- >0;){if(l=Xo(o,s,r),l===c)return i[n]=o,i[a]=s,t(i);if(l>0)o=Math.floor(o/l)*l,s=Math.ceil(s/l)*l;else if(l<0)o=Math.ceil(o*l)/l,s=Math.floor(s*l)/l;else break;c=l}return e},e}function uk(){var e=jp();return e.copy=function(){return Yp(e,uk())},vs.apply(e,arguments),hk(e)}function fk(e,t){e=e.slice();var r=0,i=e.length-1,n=e[r],a=e[i],o;return a<n&&(o=r,r=i,i=o,o=n,n=a,a=o),e[r]=t.floor(n),e[i]=t.ceil(a),e}const co=new Date,ho=new Date;function Dt(e,t,r,i){function n(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return n.floor=a=>(e(a=new Date(+a)),a),n.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),n.round=a=>{const o=n(a),s=n.ceil(a);return a-o<s-a?o:s},n.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),n.range=(a,o,s)=>{const c=[];if(a=n.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return c;let l;do c.push(l=new Date(+a)),t(a,s),e(a);while(l<a&&a<o);return c},n.filter=a=>Dt(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,s)=>{if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););}),r&&(n.count=(a,o)=>(co.setTime(+a),ho.setTime(+o),e(co),e(ho),Math.floor(r(co,ho))),n.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?n.filter(i?o=>i(o)%a===0:o=>n.count(0,o)%a===0):n)),n}const Ua=Dt(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Ua.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Dt(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Ua);Ua.range;const Ve=1e3,me=Ve*60,Xe=me*60,Je=Xe*24,Zl=Je*7,Uh=Je*30,uo=Je*365,ti=Dt(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Ve)},(e,t)=>(t-e)/Ve,e=>e.getUTCSeconds());ti.range;const Kl=Dt(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ve)},(e,t)=>{e.setTime(+e+t*me)},(e,t)=>(t-e)/me,e=>e.getMinutes());Kl.range;const dk=Dt(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*me)},(e,t)=>(t-e)/me,e=>e.getUTCMinutes());dk.range;const Ql=Dt(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ve-e.getMinutes()*me)},(e,t)=>{e.setTime(+e+t*Xe)},(e,t)=>(t-e)/Xe,e=>e.getHours());Ql.range;const pk=Dt(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Xe)},(e,t)=>(t-e)/Xe,e=>e.getUTCHours());pk.range;const Fn=Dt(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*me)/Je,e=>e.getDate()-1);Fn.range;const Jl=Dt(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Je,e=>e.getUTCDate()-1);Jl.range;const gk=Dt(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Je,e=>Math.floor(e/Je));gk.range;function Ir(e){return Dt(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*me)/Zl)}const Ss=Ir(0),Ya=Ir(1),mk=Ir(2),yk=Ir(3),vi=Ir(4),xk=Ir(5),bk=Ir(6);Ss.range;Ya.range;mk.range;yk.range;vi.range;xk.range;bk.range;function Pr(e){return Dt(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Zl)}const Gp=Pr(0),ja=Pr(1),_k=Pr(2),Ck=Pr(3),Si=Pr(4),wk=Pr(5),kk=Pr(6);Gp.range;ja.range;_k.range;Ck.range;Si.range;wk.range;kk.range;const tc=Dt(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tc.range;const vk=Dt(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());vk.range;const tr=Dt(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tr.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Dt(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});tr.range;const Fr=Dt(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Fr.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Dt(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});Fr.range;function Sk(e,t,r,i,n,a){const o=[[ti,1,Ve],[ti,5,5*Ve],[ti,15,15*Ve],[ti,30,30*Ve],[a,1,me],[a,5,5*me],[a,15,15*me],[a,30,30*me],[n,1,Xe],[n,3,3*Xe],[n,6,6*Xe],[n,12,12*Xe],[i,1,Je],[i,2,2*Je],[r,1,Zl],[t,1,Uh],[t,3,3*Uh],[e,1,uo]];function s(l,h,u){const f=h<l;f&&([l,h]=[h,l]);const d=u&&typeof u.range=="function"?u:c(l,h,u),p=d?d.range(l,+h+1):[];return f?p.reverse():p}function c(l,h,u){const f=Math.abs(h-l)/u,d=ql(([,,y])=>y).right(o,f);if(d===o.length)return e.every(Zo(l/uo,h/uo,u));if(d===0)return Ua.every(Math.max(Zo(l,h,u),1));const[p,m]=o[f/o[d-1][2]<o[d][2]/f?d-1:d];return p.every(m)}return[s,c]}const[Tk,Mk]=Sk(tr,tc,Ss,Fn,Ql,Kl);function fo(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function po(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Ui(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function Ak(e){var t=e.dateTime,r=e.date,i=e.time,n=e.periods,a=e.days,o=e.shortDays,s=e.months,c=e.shortMonths,l=Yi(n),h=ji(n),u=Yi(a),f=ji(a),d=Yi(o),p=ji(o),m=Yi(s),y=ji(s),x=Yi(c),b=ji(c),C={a:D,A:B,b:W,B:U,c:null,d:Zh,e:Zh,f:Kk,g:ov,G:cv,H:Vk,I:Xk,j:Zk,L:Vp,m:Qk,M:Jk,p:X,q:J,Q:Jh,s:tu,S:tv,u:ev,U:rv,V:iv,w:nv,W:av,x:null,X:null,y:sv,Y:lv,Z:hv,"%":Qh},v={a:it,A:ut,b:Q,B:kt,c:null,d:Kh,e:Kh,f:pv,g:vv,G:Tv,H:uv,I:fv,j:dv,L:Zp,m:gv,M:mv,p:Tt,q:It,Q:Jh,s:tu,S:yv,u:xv,U:bv,V:_v,w:Cv,W:wv,x:null,X:null,y:kv,Y:Sv,Z:Mv,"%":Qh},k={a:$,A:S,b:I,B:E,c:A,d:Vh,e:Vh,f:Uk,g:Gh,G:jh,H:Xh,I:Xh,j:zk,L:Hk,m:Nk,M:Wk,p:N,q:Pk,Q:jk,s:Gk,S:qk,u:$k,U:Dk,V:Ok,w:Fk,W:Rk,x:F,X:L,y:Gh,Y:jh,Z:Ik,"%":Yk};C.x=_(r,C),C.X=_(i,C),C.c=_(t,C),v.x=_(r,v),v.X=_(i,v),v.c=_(t,v);function _(q,G){return function(ct){var P=[],Mt=-1,dt=0,Pt=q.length,Nt,ae,pr;for(ct instanceof Date||(ct=new Date(+ct));++Mt<Pt;)q.charCodeAt(Mt)===37&&(P.push(q.slice(dt,Mt)),(ae=Yh[Nt=q.charAt(++Mt)])!=null?Nt=q.charAt(++Mt):ae=Nt==="e"?" ":"0",(pr=G[Nt])&&(Nt=pr(ct,ae)),P.push(Nt),dt=Mt+1);return P.push(q.slice(dt,Mt)),P.join("")}}function w(q,G){return function(ct){var P=Ui(1900,void 0,1),Mt=O(P,q,ct+="",0),dt,Pt;if(Mt!=ct.length)return null;if("Q"in P)return new Date(P.Q);if("s"in P)return new Date(P.s*1e3+("L"in P?P.L:0));if(G&&!("Z"in P)&&(P.Z=0),"p"in P&&(P.H=P.H%12+P.p*12),P.m===void 0&&(P.m="q"in P?P.q:0),"V"in P){if(P.V<1||P.V>53)return null;"w"in P||(P.w=1),"Z"in P?(dt=po(Ui(P.y,0,1)),Pt=dt.getUTCDay(),dt=Pt>4||Pt===0?ja.ceil(dt):ja(dt),dt=Jl.offset(dt,(P.V-1)*7),P.y=dt.getUTCFullYear(),P.m=dt.getUTCMonth(),P.d=dt.getUTCDate()+(P.w+6)%7):(dt=fo(Ui(P.y,0,1)),Pt=dt.getDay(),dt=Pt>4||Pt===0?Ya.ceil(dt):Ya(dt),dt=Fn.offset(dt,(P.V-1)*7),P.y=dt.getFullYear(),P.m=dt.getMonth(),P.d=dt.getDate()+(P.w+6)%7)}else("W"in P||"U"in P)&&("w"in P||(P.w="u"in P?P.u%7:"W"in P?1:0),Pt="Z"in P?po(Ui(P.y,0,1)).getUTCDay():fo(Ui(P.y,0,1)).getDay(),P.m=0,P.d="W"in P?(P.w+6)%7+P.W*7-(Pt+5)%7:P.w+P.U*7-(Pt+6)%7);return"Z"in P?(P.H+=P.Z/100|0,P.M+=P.Z%100,po(P)):fo(P)}}function O(q,G,ct,P){for(var Mt=0,dt=G.length,Pt=ct.length,Nt,ae;Mt<dt;){if(P>=Pt)return-1;if(Nt=G.charCodeAt(Mt++),Nt===37){if(Nt=G.charAt(Mt++),ae=k[Nt in Yh?G.charAt(Mt++):Nt],!ae||(P=ae(q,ct,P))<0)return-1}else if(Nt!=ct.charCodeAt(P++))return-1}return P}function N(q,G,ct){var P=l.exec(G.slice(ct));return P?(q.p=h.get(P[0].toLowerCase()),ct+P[0].length):-1}function $(q,G,ct){var P=d.exec(G.slice(ct));return P?(q.w=p.get(P[0].toLowerCase()),ct+P[0].length):-1}function S(q,G,ct){var P=u.exec(G.slice(ct));return P?(q.w=f.get(P[0].toLowerCase()),ct+P[0].length):-1}function I(q,G,ct){var P=x.exec(G.slice(ct));return P?(q.m=b.get(P[0].toLowerCase()),ct+P[0].length):-1}function E(q,G,ct){var P=m.exec(G.slice(ct));return P?(q.m=y.get(P[0].toLowerCase()),ct+P[0].length):-1}function A(q,G,ct){return O(q,t,G,ct)}function F(q,G,ct){return O(q,r,G,ct)}function L(q,G,ct){return O(q,i,G,ct)}function D(q){return o[q.getDay()]}function B(q){return a[q.getDay()]}function W(q){return c[q.getMonth()]}function U(q){return s[q.getMonth()]}function X(q){return n[+(q.getHours()>=12)]}function J(q){return 1+~~(q.getMonth()/3)}function it(q){return o[q.getUTCDay()]}function ut(q){return a[q.getUTCDay()]}function Q(q){return c[q.getUTCMonth()]}function kt(q){return s[q.getUTCMonth()]}function Tt(q){return n[+(q.getUTCHours()>=12)]}function It(q){return 1+~~(q.getUTCMonth()/3)}return{format:function(q){var G=_(q+="",C);return G.toString=function(){return q},G},parse:function(q){var G=w(q+="",!1);return G.toString=function(){return q},G},utcFormat:function(q){var G=_(q+="",v);return G.toString=function(){return q},G},utcParse:function(q){var G=w(q+="",!0);return G.toString=function(){return q},G}}}var Yh={"-":"",_:" ",0:"0"},Rt=/^\s*\d+/,Lk=/^%/,Bk=/[\\^$*+?|[\]().{}]/g;function mt(e,t,r){var i=e<0?"-":"",n=(i?-e:e)+"",a=n.length;return i+(a<r?new Array(r-a+1).join(t)+n:n)}function Ek(e){return e.replace(Bk,"\\$&")}function Yi(e){return new RegExp("^(?:"+e.map(Ek).join("|")+")","i")}function ji(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function Fk(e,t,r){var i=Rt.exec(t.slice(r,r+1));return i?(e.w=+i[0],r+i[0].length):-1}function $k(e,t,r){var i=Rt.exec(t.slice(r,r+1));return i?(e.u=+i[0],r+i[0].length):-1}function Dk(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.U=+i[0],r+i[0].length):-1}function Ok(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.V=+i[0],r+i[0].length):-1}function Rk(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.W=+i[0],r+i[0].length):-1}function jh(e,t,r){var i=Rt.exec(t.slice(r,r+4));return i?(e.y=+i[0],r+i[0].length):-1}function Gh(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.y=+i[0]+(+i[0]>68?1900:2e3),r+i[0].length):-1}function Ik(e,t,r){var i=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return i?(e.Z=i[1]?0:-(i[2]+(i[3]||"00")),r+i[0].length):-1}function Pk(e,t,r){var i=Rt.exec(t.slice(r,r+1));return i?(e.q=i[0]*3-3,r+i[0].length):-1}function Nk(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.m=i[0]-1,r+i[0].length):-1}function Vh(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.d=+i[0],r+i[0].length):-1}function zk(e,t,r){var i=Rt.exec(t.slice(r,r+3));return i?(e.m=0,e.d=+i[0],r+i[0].length):-1}function Xh(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.H=+i[0],r+i[0].length):-1}function Wk(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.M=+i[0],r+i[0].length):-1}function qk(e,t,r){var i=Rt.exec(t.slice(r,r+2));return i?(e.S=+i[0],r+i[0].length):-1}function Hk(e,t,r){var i=Rt.exec(t.slice(r,r+3));return i?(e.L=+i[0],r+i[0].length):-1}function Uk(e,t,r){var i=Rt.exec(t.slice(r,r+6));return i?(e.L=Math.floor(i[0]/1e3),r+i[0].length):-1}function Yk(e,t,r){var i=Lk.exec(t.slice(r,r+1));return i?r+i[0].length:-1}function jk(e,t,r){var i=Rt.exec(t.slice(r));return i?(e.Q=+i[0],r+i[0].length):-1}function Gk(e,t,r){var i=Rt.exec(t.slice(r));return i?(e.s=+i[0],r+i[0].length):-1}function Zh(e,t){return mt(e.getDate(),t,2)}function Vk(e,t){return mt(e.getHours(),t,2)}function Xk(e,t){return mt(e.getHours()%12||12,t,2)}function Zk(e,t){return mt(1+Fn.count(tr(e),e),t,3)}function Vp(e,t){return mt(e.getMilliseconds(),t,3)}function Kk(e,t){return Vp(e,t)+"000"}function Qk(e,t){return mt(e.getMonth()+1,t,2)}function Jk(e,t){return mt(e.getMinutes(),t,2)}function tv(e,t){return mt(e.getSeconds(),t,2)}function ev(e){var t=e.getDay();return t===0?7:t}function rv(e,t){return mt(Ss.count(tr(e)-1,e),t,2)}function Xp(e){var t=e.getDay();return t>=4||t===0?vi(e):vi.ceil(e)}function iv(e,t){return e=Xp(e),mt(vi.count(tr(e),e)+(tr(e).getDay()===4),t,2)}function nv(e){return e.getDay()}function av(e,t){return mt(Ya.count(tr(e)-1,e),t,2)}function sv(e,t){return mt(e.getFullYear()%100,t,2)}function ov(e,t){return e=Xp(e),mt(e.getFullYear()%100,t,2)}function lv(e,t){return mt(e.getFullYear()%1e4,t,4)}function cv(e,t){var r=e.getDay();return e=r>=4||r===0?vi(e):vi.ceil(e),mt(e.getFullYear()%1e4,t,4)}function hv(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+mt(t/60|0,"0",2)+mt(t%60,"0",2)}function Kh(e,t){return mt(e.getUTCDate(),t,2)}function uv(e,t){return mt(e.getUTCHours(),t,2)}function fv(e,t){return mt(e.getUTCHours()%12||12,t,2)}function dv(e,t){return mt(1+Jl.count(Fr(e),e),t,3)}function Zp(e,t){return mt(e.getUTCMilliseconds(),t,3)}function pv(e,t){return Zp(e,t)+"000"}function gv(e,t){return mt(e.getUTCMonth()+1,t,2)}function mv(e,t){return mt(e.getUTCMinutes(),t,2)}function yv(e,t){return mt(e.getUTCSeconds(),t,2)}function xv(e){var t=e.getUTCDay();return t===0?7:t}function bv(e,t){return mt(Gp.count(Fr(e)-1,e),t,2)}function Kp(e){var t=e.getUTCDay();return t>=4||t===0?Si(e):Si.ceil(e)}function _v(e,t){return e=Kp(e),mt(Si.count(Fr(e),e)+(Fr(e).getUTCDay()===4),t,2)}function Cv(e){return e.getUTCDay()}function wv(e,t){return mt(ja.count(Fr(e)-1,e),t,2)}function kv(e,t){return mt(e.getUTCFullYear()%100,t,2)}function vv(e,t){return e=Kp(e),mt(e.getUTCFullYear()%100,t,2)}function Sv(e,t){return mt(e.getUTCFullYear()%1e4,t,4)}function Tv(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Si(e):Si.ceil(e),mt(e.getUTCFullYear()%1e4,t,4)}function Mv(){return"+0000"}function Qh(){return"%"}function Jh(e){return+e}function tu(e){return Math.floor(+e/1e3)}var Vr,Qp;Av({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Av(e){return Vr=Ak(e),Qp=Vr.format,Vr.parse,Vr.utcFormat,Vr.utcParse,Vr}function Lv(e){return new Date(e)}function Bv(e){return e instanceof Date?+e:+new Date(+e)}function Jp(e,t,r,i,n,a,o,s,c,l){var h=jp(),u=h.invert,f=h.domain,d=l(".%L"),p=l(":%S"),m=l("%I:%M"),y=l("%I %p"),x=l("%a %d"),b=l("%b %d"),C=l("%B"),v=l("%Y");function k(_){return(c(_)<_?d:s(_)<_?p:o(_)<_?m:a(_)<_?y:i(_)<_?n(_)<_?x:b:r(_)<_?C:v)(_)}return h.invert=function(_){return new Date(u(_))},h.domain=function(_){return arguments.length?f(Array.from(_,Bv)):f().map(Lv)},h.ticks=function(_){var w=f();return e(w[0],w[w.length-1],_??10)},h.tickFormat=function(_,w){return w==null?k:l(w)},h.nice=function(_){var w=f();return(!_||typeof _.range!="function")&&(_=t(w[0],w[w.length-1],_??10)),_?f(fk(w,_)):h},h.copy=function(){return Yp(h,Jp(e,t,r,i,n,a,o,s,c,l))},h}function p3(){return vs.apply(Jp(Tk,Mk,tr,tc,Ss,Fn,Ql,Kl,ti,Qp).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Ev(e){for(var t=e.length/6|0,r=new Array(t),i=0;i<t;)r[i]="#"+e.slice(i*6,++i*6);return r}const g3=Ev("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function At(e){return function(){return e}}const eu=Math.abs,Yt=Math.atan2,mr=Math.cos,Fv=Math.max,go=Math.min,Be=Math.sin,ei=Math.sqrt,Gt=1e-12,wn=Math.PI,Ga=wn/2,ga=2*wn;function $v(e){return e>1?0:e<-1?wn:Math.acos(e)}function ru(e){return e>=1?Ga:e<=-1?-Ga:Math.asin(e)}function tg(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const i=Math.floor(r);if(!(i>=0))throw new RangeError(`invalid digits: ${r}`);t=i}return e},()=>new Uw(t)}function Dv(e){return e.innerRadius}function Ov(e){return e.outerRadius}function Rv(e){return e.startAngle}function Iv(e){return e.endAngle}function Pv(e){return e&&e.padAngle}function Nv(e,t,r,i,n,a,o,s){var c=r-e,l=i-t,h=o-n,u=s-a,f=u*c-h*l;if(!(f*f<Gt))return f=(h*(t-a)-u*(e-n))/f,[e+f*c,t+f*l]}function Kn(e,t,r,i,n,a,o){var s=e-r,c=t-i,l=(o?a:-a)/ei(s*s+c*c),h=l*c,u=-l*s,f=e+h,d=t+u,p=r+h,m=i+u,y=(f+p)/2,x=(d+m)/2,b=p-f,C=m-d,v=b*b+C*C,k=n-a,_=f*m-p*d,w=(C<0?-1:1)*ei(Fv(0,k*k*v-_*_)),O=(_*C-b*w)/v,N=(-_*b-C*w)/v,$=(_*C+b*w)/v,S=(-_*b+C*w)/v,I=O-y,E=N-x,A=$-y,F=S-x;return I*I+E*E>A*A+F*F&&(O=$,N=S),{cx:O,cy:N,x01:-h,y01:-u,x11:O*(n/k-1),y11:N*(n/k-1)}}function m3(){var e=Dv,t=Ov,r=At(0),i=null,n=Rv,a=Iv,o=Pv,s=null,c=tg(l);function l(){var h,u,f=+e.apply(this,arguments),d=+t.apply(this,arguments),p=n.apply(this,arguments)-Ga,m=a.apply(this,arguments)-Ga,y=eu(m-p),x=m>p;if(s||(s=h=c()),d<f&&(u=d,d=f,f=u),!(d>Gt))s.moveTo(0,0);else if(y>ga-Gt)s.moveTo(d*mr(p),d*Be(p)),s.arc(0,0,d,p,m,!x),f>Gt&&(s.moveTo(f*mr(m),f*Be(m)),s.arc(0,0,f,m,p,x));else{var b=p,C=m,v=p,k=m,_=y,w=y,O=o.apply(this,arguments)/2,N=O>Gt&&(i?+i.apply(this,arguments):ei(f*f+d*d)),$=go(eu(d-f)/2,+r.apply(this,arguments)),S=$,I=$,E,A;if(N>Gt){var F=ru(N/f*Be(O)),L=ru(N/d*Be(O));(_-=F*2)>Gt?(F*=x?1:-1,v+=F,k-=F):(_=0,v=k=(p+m)/2),(w-=L*2)>Gt?(L*=x?1:-1,b+=L,C-=L):(w=0,b=C=(p+m)/2)}var D=d*mr(b),B=d*Be(b),W=f*mr(k),U=f*Be(k);if($>Gt){var X=d*mr(C),J=d*Be(C),it=f*mr(v),ut=f*Be(v),Q;if(y<wn)if(Q=Nv(D,B,it,ut,X,J,W,U)){var kt=D-Q[0],Tt=B-Q[1],It=X-Q[0],q=J-Q[1],G=1/Be($v((kt*It+Tt*q)/(ei(kt*kt+Tt*Tt)*ei(It*It+q*q)))/2),ct=ei(Q[0]*Q[0]+Q[1]*Q[1]);S=go($,(f-ct)/(G-1)),I=go($,(d-ct)/(G+1))}else S=I=0}w>Gt?I>Gt?(E=Kn(it,ut,D,B,d,I,x),A=Kn(X,J,W,U,d,I,x),s.moveTo(E.cx+E.x01,E.cy+E.y01),I<$?s.arc(E.cx,E.cy,I,Yt(E.y01,E.x01),Yt(A.y01,A.x01),!x):(s.arc(E.cx,E.cy,I,Yt(E.y01,E.x01),Yt(E.y11,E.x11),!x),s.arc(0,0,d,Yt(E.cy+E.y11,E.cx+E.x11),Yt(A.cy+A.y11,A.cx+A.x11),!x),s.arc(A.cx,A.cy,I,Yt(A.y11,A.x11),Yt(A.y01,A.x01),!x))):(s.moveTo(D,B),s.arc(0,0,d,b,C,!x)):s.moveTo(D,B),!(f>Gt)||!(_>Gt)?s.lineTo(W,U):S>Gt?(E=Kn(W,U,X,J,f,-S,x),A=Kn(D,B,it,ut,f,-S,x),s.lineTo(E.cx+E.x01,E.cy+E.y01),S<$?s.arc(E.cx,E.cy,S,Yt(E.y01,E.x01),Yt(A.y01,A.x01),!x):(s.arc(E.cx,E.cy,S,Yt(E.y01,E.x01),Yt(E.y11,E.x11),!x),s.arc(0,0,f,Yt(E.cy+E.y11,E.cx+E.x11),Yt(A.cy+A.y11,A.cx+A.x11),x),s.arc(A.cx,A.cy,S,Yt(A.y11,A.x11),Yt(A.y01,A.x01),!x))):s.arc(0,0,f,k,v,x)}if(s.closePath(),h)return s=null,h+""||null}return l.centroid=function(){var h=(+e.apply(this,arguments)+ +t.apply(this,arguments))/2,u=(+n.apply(this,arguments)+ +a.apply(this,arguments))/2-wn/2;return[mr(u)*h,Be(u)*h]},l.innerRadius=function(h){return arguments.length?(e=typeof h=="function"?h:At(+h),l):e},l.outerRadius=function(h){return arguments.length?(t=typeof h=="function"?h:At(+h),l):t},l.cornerRadius=function(h){return arguments.length?(r=typeof h=="function"?h:At(+h),l):r},l.padRadius=function(h){return arguments.length?(i=h==null?null:typeof h=="function"?h:At(+h),l):i},l.startAngle=function(h){return arguments.length?(n=typeof h=="function"?h:At(+h),l):n},l.endAngle=function(h){return arguments.length?(a=typeof h=="function"?h:At(+h),l):a},l.padAngle=function(h){return arguments.length?(o=typeof h=="function"?h:At(+h),l):o},l.context=function(h){return arguments.length?(s=h??null,l):s},l}function eg(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function rg(e){this._context=e}rg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Va(e){return new rg(e)}function zv(e){return e[0]}function Wv(e){return e[1]}function qv(e,t){var r=At(!0),i=null,n=Va,a=null,o=tg(s);e=typeof e=="function"?e:e===void 0?zv:At(e),t=typeof t=="function"?t:t===void 0?Wv:At(t);function s(c){var l,h=(c=eg(c)).length,u,f=!1,d;for(i==null&&(a=n(d=o())),l=0;l<=h;++l)!(l<h&&r(u=c[l],l,c))===f&&((f=!f)?a.lineStart():a.lineEnd()),f&&a.point(+e(u,l,c),+t(u,l,c));if(d)return a=null,d+""||null}return s.x=function(c){return arguments.length?(e=typeof c=="function"?c:At(+c),s):e},s.y=function(c){return arguments.length?(t=typeof c=="function"?c:At(+c),s):t},s.defined=function(c){return arguments.length?(r=typeof c=="function"?c:At(!!c),s):r},s.curve=function(c){return arguments.length?(n=c,i!=null&&(a=n(i)),s):n},s.context=function(c){return arguments.length?(c==null?i=a=null:a=n(i=c),s):i},s}function Hv(e,t){return t<e?-1:t>e?1:t>=e?0:NaN}function Uv(e){return e}function y3(){var e=Uv,t=Hv,r=null,i=At(0),n=At(ga),a=At(0);function o(s){var c,l=(s=eg(s)).length,h,u,f=0,d=new Array(l),p=new Array(l),m=+i.apply(this,arguments),y=Math.min(ga,Math.max(-ga,n.apply(this,arguments)-m)),x,b=Math.min(Math.abs(y)/l,a.apply(this,arguments)),C=b*(y<0?-1:1),v;for(c=0;c<l;++c)(v=p[d[c]=c]=+e(s[c],c,s))>0&&(f+=v);for(t!=null?d.sort(function(k,_){return t(p[k],p[_])}):r!=null&&d.sort(function(k,_){return r(s[k],s[_])}),c=0,u=f?(y-l*C)/f:0;c<l;++c,m=x)h=d[c],v=p[h],x=m+(v>0?v*u:0)+C,p[h]={data:s[h],index:c,value:v,startAngle:m,endAngle:x,padAngle:b};return p}return o.value=function(s){return arguments.length?(e=typeof s=="function"?s:At(+s),o):e},o.sortValues=function(s){return arguments.length?(t=s,r=null,o):t},o.sort=function(s){return arguments.length?(r=s,t=null,o):r},o.startAngle=function(s){return arguments.length?(i=typeof s=="function"?s:At(+s),o):i},o.endAngle=function(s){return arguments.length?(n=typeof s=="function"?s:At(+s),o):n},o.padAngle=function(s){return arguments.length?(a=typeof s=="function"?s:At(+s),o):a},o}class ig{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function ng(e){return new ig(e,!0)}function ag(e){return new ig(e,!1)}function cr(){}function Xa(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Ts(e){this._context=e}Ts.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Xa(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Xa(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function ma(e){return new Ts(e)}function sg(e){this._context=e}sg.prototype={areaStart:cr,areaEnd:cr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Xa(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Yv(e){return new sg(e)}function og(e){this._context=e}og.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,i=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,i):this._context.moveTo(r,i);break;case 3:this._point=4;default:Xa(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function jv(e){return new og(e)}function lg(e,t){this._basis=new Ts(e),this._beta=t}lg.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var e=this._x,t=this._y,r=e.length-1;if(r>0)for(var i=e[0],n=t[0],a=e[r]-i,o=t[r]-n,s=-1,c;++s<=r;)c=s/r,this._basis.point(this._beta*e[s]+(1-this._beta)*(i+c*a),this._beta*t[s]+(1-this._beta)*(n+c*o));this._x=this._y=null,this._basis.lineEnd()},point:function(e,t){this._x.push(+e),this._y.push(+t)}};const Gv=function e(t){function r(i){return t===1?new Ts(i):new lg(i,t)}return r.beta=function(i){return e(+i)},r}(.85);function Za(e,t,r){e._context.bezierCurveTo(e._x1+e._k*(e._x2-e._x0),e._y1+e._k*(e._y2-e._y0),e._x2+e._k*(e._x1-t),e._y2+e._k*(e._y1-r),e._x2,e._y2)}function ec(e,t){this._context=e,this._k=(1-t)/6}ec.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:Za(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2,this._x1=e,this._y1=t;break;case 2:this._point=3;default:Za(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const cg=function e(t){function r(i){return new ec(i,t)}return r.tension=function(i){return e(+i)},r}(0);function rc(e,t){this._context=e,this._k=(1-t)/6}rc.prototype={areaStart:cr,areaEnd:cr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:Za(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Vv=function e(t){function r(i){return new rc(i,t)}return r.tension=function(i){return e(+i)},r}(0);function ic(e,t){this._context=e,this._k=(1-t)/6}ic.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Za(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Xv=function e(t){function r(i){return new ic(i,t)}return r.tension=function(i){return e(+i)},r}(0);function nc(e,t,r){var i=e._x1,n=e._y1,a=e._x2,o=e._y2;if(e._l01_a>Gt){var s=2*e._l01_2a+3*e._l01_a*e._l12_a+e._l12_2a,c=3*e._l01_a*(e._l01_a+e._l12_a);i=(i*s-e._x0*e._l12_2a+e._x2*e._l01_2a)/c,n=(n*s-e._y0*e._l12_2a+e._y2*e._l01_2a)/c}if(e._l23_a>Gt){var l=2*e._l23_2a+3*e._l23_a*e._l12_a+e._l12_2a,h=3*e._l23_a*(e._l23_a+e._l12_a);a=(a*l+e._x1*e._l23_2a-t*e._l12_2a)/h,o=(o*l+e._y1*e._l23_2a-r*e._l12_2a)/h}e._context.bezierCurveTo(i,n,a,o,e._x2,e._y2)}function hg(e,t){this._context=e,this._alpha=t}hg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3;default:nc(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const ug=function e(t){function r(i){return t?new hg(i,t):new ec(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function fg(e,t){this._context=e,this._alpha=t}fg.prototype={areaStart:cr,areaEnd:cr,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:nc(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Zv=function e(t){function r(i){return t?new fg(i,t):new rc(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function dg(e,t){this._context=e,this._alpha=t}dg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:nc(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Kv=function e(t){function r(i){return t?new dg(i,t):new ic(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function pg(e){this._context=e}pg.prototype={areaStart:cr,areaEnd:cr,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Qv(e){return new pg(e)}function iu(e){return e<0?-1:1}function nu(e,t,r){var i=e._x1-e._x0,n=t-e._x1,a=(e._y1-e._y0)/(i||n<0&&-0),o=(r-e._y1)/(n||i<0&&-0),s=(a*n+o*i)/(i+n);return(iu(a)+iu(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function au(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function mo(e,t,r){var i=e._x0,n=e._y0,a=e._x1,o=e._y1,s=(a-i)/3;e._context.bezierCurveTo(i+s,n+s*t,a-s,o-s*r,a,o)}function Ka(e){this._context=e}Ka.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:mo(this,this._t0,au(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,mo(this,au(this,r=nu(this,e,t)),r);break;default:mo(this,this._t0,r=nu(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function gg(e){this._context=new mg(e)}(gg.prototype=Object.create(Ka.prototype)).point=function(e,t){Ka.prototype.point.call(this,t,e)};function mg(e){this._context=e}mg.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,i,n,a){this._context.bezierCurveTo(t,e,i,r,a,n)}};function yg(e){return new Ka(e)}function xg(e){return new gg(e)}function bg(e){this._context=e}bg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var i=su(e),n=su(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(i[0][a],n[0][a],i[1][a],n[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function su(e){var t,r=e.length-1,i,n=new Array(r),a=new Array(r),o=new Array(r);for(n[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)n[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(n[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)i=n[t]/a[t-1],a[t]-=i,o[t]-=i*o[t-1];for(n[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)n[t]=(o[t]-n[t+1])/a[t];for(a[r-1]=(e[r]+n[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-n[t+1];return[n,a]}function _g(e){return new bg(e)}function Ms(e,t){this._context=e,this._t=t}Ms.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Cg(e){return new Ms(e,.5)}function wg(e){return new Ms(e,0)}function kg(e){return new Ms(e,1)}function nn(e,t,r){this.k=e,this.x=t,this.y=r}nn.prototype={constructor:nn,scale:function(e){return e===1?this:new nn(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new nn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};nn.prototype;var vg=typeof global=="object"&&global&&global.Object===Object&&global,Jv=typeof self=="object"&&self&&self.Object===Object&&self,Ne=vg||Jv||Function("return this")(),Qa=Ne.Symbol,Sg=Object.prototype,tS=Sg.hasOwnProperty,eS=Sg.toString,Gi=Qa?Qa.toStringTag:void 0;function rS(e){var t=tS.call(e,Gi),r=e[Gi];try{e[Gi]=void 0;var i=!0}catch{}var n=eS.call(e);return i&&(t?e[Gi]=r:delete e[Gi]),n}var iS=Object.prototype,nS=iS.toString;function aS(e){return nS.call(e)}var sS="[object Null]",oS="[object Undefined]",ou=Qa?Qa.toStringTag:void 0;function Li(e){return e==null?e===void 0?oS:sS:ou&&ou in Object(e)?rS(e):aS(e)}function Nr(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var lS="[object AsyncFunction]",cS="[object Function]",hS="[object GeneratorFunction]",uS="[object Proxy]";function ac(e){if(!Nr(e))return!1;var t=Li(e);return t==cS||t==hS||t==lS||t==uS}var yo=Ne["__core-js_shared__"],lu=function(){var e=/[^.]+$/.exec(yo&&yo.keys&&yo.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function fS(e){return!!lu&&lu in e}var dS=Function.prototype,pS=dS.toString;function zr(e){if(e!=null){try{return pS.call(e)}catch{}try{return e+""}catch{}}return""}var gS=/[\\^$.*+?()[\]{}|]/g,mS=/^\[object .+?Constructor\]$/,yS=Function.prototype,xS=Object.prototype,bS=yS.toString,_S=xS.hasOwnProperty,CS=RegExp("^"+bS.call(_S).replace(gS,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function wS(e){if(!Nr(e)||fS(e))return!1;var t=ac(e)?CS:mS;return t.test(zr(e))}function kS(e,t){return e==null?void 0:e[t]}function Wr(e,t){var r=kS(e,t);return wS(r)?r:void 0}var kn=Wr(Object,"create");function vS(){this.__data__=kn?kn(null):{},this.size=0}function SS(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var TS="__lodash_hash_undefined__",MS=Object.prototype,AS=MS.hasOwnProperty;function LS(e){var t=this.__data__;if(kn){var r=t[e];return r===TS?void 0:r}return AS.call(t,e)?t[e]:void 0}var BS=Object.prototype,ES=BS.hasOwnProperty;function FS(e){var t=this.__data__;return kn?t[e]!==void 0:ES.call(t,e)}var $S="__lodash_hash_undefined__";function DS(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=kn&&t===void 0?$S:t,this}function $r(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}$r.prototype.clear=vS;$r.prototype.delete=SS;$r.prototype.get=LS;$r.prototype.has=FS;$r.prototype.set=DS;function OS(){this.__data__=[],this.size=0}function As(e,t){return e===t||e!==e&&t!==t}function Ls(e,t){for(var r=e.length;r--;)if(As(e[r][0],t))return r;return-1}var RS=Array.prototype,IS=RS.splice;function PS(e){var t=this.__data__,r=Ls(t,e);if(r<0)return!1;var i=t.length-1;return r==i?t.pop():IS.call(t,r,1),--this.size,!0}function NS(e){var t=this.__data__,r=Ls(t,e);return r<0?void 0:t[r][1]}function zS(e){return Ls(this.__data__,e)>-1}function WS(e,t){var r=this.__data__,i=Ls(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this}function rr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}rr.prototype.clear=OS;rr.prototype.delete=PS;rr.prototype.get=NS;rr.prototype.has=zS;rr.prototype.set=WS;var vn=Wr(Ne,"Map");function qS(){this.size=0,this.__data__={hash:new $r,map:new(vn||rr),string:new $r}}function HS(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Bs(e,t){var r=e.__data__;return HS(t)?r[typeof t=="string"?"string":"hash"]:r.map}function US(e){var t=Bs(this,e).delete(e);return this.size-=t?1:0,t}function YS(e){return Bs(this,e).get(e)}function jS(e){return Bs(this,e).has(e)}function GS(e,t){var r=Bs(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this}function fr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}fr.prototype.clear=qS;fr.prototype.delete=US;fr.prototype.get=YS;fr.prototype.has=jS;fr.prototype.set=GS;var VS="Expected a function";function $n(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(VS);var r=function(){var i=arguments,n=t?t.apply(this,i):i[0],a=r.cache;if(a.has(n))return a.get(n);var o=e.apply(this,i);return r.cache=a.set(n,o)||a,o};return r.cache=new($n.Cache||fr),r}$n.Cache=fr;function XS(){this.__data__=new rr,this.size=0}function ZS(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function KS(e){return this.__data__.get(e)}function QS(e){return this.__data__.has(e)}var JS=200;function tT(e,t){var r=this.__data__;if(r instanceof rr){var i=r.__data__;if(!vn||i.length<JS-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new fr(i)}return r.set(e,t),this.size=r.size,this}function Bi(e){var t=this.__data__=new rr(e);this.size=t.size}Bi.prototype.clear=XS;Bi.prototype.delete=ZS;Bi.prototype.get=KS;Bi.prototype.has=QS;Bi.prototype.set=tT;var Ja=function(){try{var e=Wr(Object,"defineProperty");return e({},"",{}),e}catch{}}();function sc(e,t,r){t=="__proto__"&&Ja?Ja(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function cl(e,t,r){(r!==void 0&&!As(e[t],r)||r===void 0&&!(t in e))&&sc(e,t,r)}function eT(e){return function(t,r,i){for(var n=-1,a=Object(t),o=i(t),s=o.length;s--;){var c=o[++n];if(r(a[c],c,a)===!1)break}return t}}var rT=eT(),Tg=typeof exports=="object"&&exports&&!exports.nodeType&&exports,cu=Tg&&typeof module=="object"&&module&&!module.nodeType&&module,iT=cu&&cu.exports===Tg,hu=iT?Ne.Buffer:void 0,uu=hu?hu.allocUnsafe:void 0;function nT(e,t){if(t)return e.slice();var r=e.length,i=uu?uu(r):new e.constructor(r);return e.copy(i),i}var fu=Ne.Uint8Array;function aT(e){var t=new e.constructor(e.byteLength);return new fu(t).set(new fu(e)),t}function sT(e,t){var r=t?aT(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function oT(e,t){var r=-1,i=e.length;for(t||(t=Array(i));++r<i;)t[r]=e[r];return t}var du=Object.create,lT=function(){function e(){}return function(t){if(!Nr(t))return{};if(du)return du(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function Mg(e,t){return function(r){return e(t(r))}}var Ag=Mg(Object.getPrototypeOf,Object),cT=Object.prototype;function Es(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||cT;return e===r}function hT(e){return typeof e.constructor=="function"&&!Es(e)?lT(Ag(e)):{}}function Dn(e){return e!=null&&typeof e=="object"}var uT="[object Arguments]";function pu(e){return Dn(e)&&Li(e)==uT}var Lg=Object.prototype,fT=Lg.hasOwnProperty,dT=Lg.propertyIsEnumerable,ts=pu(function(){return arguments}())?pu:function(e){return Dn(e)&&fT.call(e,"callee")&&!dT.call(e,"callee")},es=Array.isArray,pT=9007199254740991;function Bg(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=pT}function Fs(e){return e!=null&&Bg(e.length)&&!ac(e)}function gT(e){return Dn(e)&&Fs(e)}function mT(){return!1}var Eg=typeof exports=="object"&&exports&&!exports.nodeType&&exports,gu=Eg&&typeof module=="object"&&module&&!module.nodeType&&module,yT=gu&&gu.exports===Eg,mu=yT?Ne.Buffer:void 0,xT=mu?mu.isBuffer:void 0,oc=xT||mT,bT="[object Object]",_T=Function.prototype,CT=Object.prototype,Fg=_T.toString,wT=CT.hasOwnProperty,kT=Fg.call(Object);function vT(e){if(!Dn(e)||Li(e)!=bT)return!1;var t=Ag(e);if(t===null)return!0;var r=wT.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Fg.call(r)==kT}var ST="[object Arguments]",TT="[object Array]",MT="[object Boolean]",AT="[object Date]",LT="[object Error]",BT="[object Function]",ET="[object Map]",FT="[object Number]",$T="[object Object]",DT="[object RegExp]",OT="[object Set]",RT="[object String]",IT="[object WeakMap]",PT="[object ArrayBuffer]",NT="[object DataView]",zT="[object Float32Array]",WT="[object Float64Array]",qT="[object Int8Array]",HT="[object Int16Array]",UT="[object Int32Array]",YT="[object Uint8Array]",jT="[object Uint8ClampedArray]",GT="[object Uint16Array]",VT="[object Uint32Array]",wt={};wt[zT]=wt[WT]=wt[qT]=wt[HT]=wt[UT]=wt[YT]=wt[jT]=wt[GT]=wt[VT]=!0;wt[ST]=wt[TT]=wt[PT]=wt[MT]=wt[NT]=wt[AT]=wt[LT]=wt[BT]=wt[ET]=wt[FT]=wt[$T]=wt[DT]=wt[OT]=wt[RT]=wt[IT]=!1;function XT(e){return Dn(e)&&Bg(e.length)&&!!wt[Li(e)]}function ZT(e){return function(t){return e(t)}}var $g=typeof exports=="object"&&exports&&!exports.nodeType&&exports,fn=$g&&typeof module=="object"&&module&&!module.nodeType&&module,KT=fn&&fn.exports===$g,xo=KT&&vg.process,yu=function(){try{var e=fn&&fn.require&&fn.require("util").types;return e||xo&&xo.binding&&xo.binding("util")}catch{}}(),xu=yu&&yu.isTypedArray,lc=xu?ZT(xu):XT;function hl(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var QT=Object.prototype,JT=QT.hasOwnProperty;function tM(e,t,r){var i=e[t];(!(JT.call(e,t)&&As(i,r))||r===void 0&&!(t in e))&&sc(e,t,r)}function eM(e,t,r,i){var n=!r;r||(r={});for(var a=-1,o=t.length;++a<o;){var s=t[a],c=void 0;c===void 0&&(c=e[s]),n?sc(r,s,c):tM(r,s,c)}return r}function rM(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}var iM=9007199254740991,nM=/^(?:0|[1-9]\d*)$/;function Dg(e,t){var r=typeof e;return t=t??iM,!!t&&(r=="number"||r!="symbol"&&nM.test(e))&&e>-1&&e%1==0&&e<t}var aM=Object.prototype,sM=aM.hasOwnProperty;function oM(e,t){var r=es(e),i=!r&&ts(e),n=!r&&!i&&oc(e),a=!r&&!i&&!n&&lc(e),o=r||i||n||a,s=o?rM(e.length,String):[],c=s.length;for(var l in e)(t||sM.call(e,l))&&!(o&&(l=="length"||n&&(l=="offset"||l=="parent")||a&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||Dg(l,c)))&&s.push(l);return s}function lM(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var cM=Object.prototype,hM=cM.hasOwnProperty;function uM(e){if(!Nr(e))return lM(e);var t=Es(e),r=[];for(var i in e)i=="constructor"&&(t||!hM.call(e,i))||r.push(i);return r}function Og(e){return Fs(e)?oM(e,!0):uM(e)}function fM(e){return eM(e,Og(e))}function dM(e,t,r,i,n,a,o){var s=hl(e,r),c=hl(t,r),l=o.get(c);if(l){cl(e,r,l);return}var h=a?a(s,c,r+"",e,t,o):void 0,u=h===void 0;if(u){var f=es(c),d=!f&&oc(c),p=!f&&!d&&lc(c);h=c,f||d||p?es(s)?h=s:gT(s)?h=oT(s):d?(u=!1,h=nT(c,!0)):p?(u=!1,h=sT(c,!0)):h=[]:vT(c)||ts(c)?(h=s,ts(s)?h=fM(s):(!Nr(s)||ac(s))&&(h=hT(c))):u=!1}u&&(o.set(c,h),n(h,c,i,a,o),o.delete(c)),cl(e,r,h)}function Rg(e,t,r,i,n){e!==t&&rT(t,function(a,o){if(n||(n=new Bi),Nr(a))dM(e,t,o,r,Rg,i,n);else{var s=i?i(hl(e,o),a,o+"",e,t,n):void 0;s===void 0&&(s=a),cl(e,o,s)}},Og)}function Ig(e){return e}function pM(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var bu=Math.max;function gM(e,t,r){return t=bu(t===void 0?e.length-1:t,0),function(){for(var i=arguments,n=-1,a=bu(i.length-t,0),o=Array(a);++n<a;)o[n]=i[t+n];n=-1;for(var s=Array(t+1);++n<t;)s[n]=i[n];return s[t]=r(o),pM(e,this,s)}}function mM(e){return function(){return e}}var yM=Ja?function(e,t){return Ja(e,"toString",{configurable:!0,enumerable:!1,value:mM(t),writable:!0})}:Ig,xM=800,bM=16,_M=Date.now;function CM(e){var t=0,r=0;return function(){var i=_M(),n=bM-(i-r);if(r=i,n>0){if(++t>=xM)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var wM=CM(yM);function kM(e,t){return wM(gM(e,t,Ig),e+"")}function vM(e,t,r){if(!Nr(r))return!1;var i=typeof t;return(i=="number"?Fs(r)&&Dg(t,r.length):i=="string"&&t in r)?As(r[t],e):!1}function SM(e){return kM(function(t,r){var i=-1,n=r.length,a=n>1?r[n-1]:void 0,o=n>2?r[2]:void 0;for(a=e.length>3&&typeof a=="function"?(n--,a):void 0,o&&vM(r[0],r[1],o)&&(a=n<3?void 0:a,n=1),t=Object(t);++i<n;){var s=r[i];s&&e(t,s,i,a)}return t})}var TM=SM(function(e,t,r){Rg(e,t,r)}),MM="​",AM={curveBasis:ma,curveBasisClosed:Yv,curveBasisOpen:jv,curveBumpX:ng,curveBumpY:ag,curveBundle:Gv,curveCardinalClosed:Vv,curveCardinalOpen:Xv,curveCardinal:cg,curveCatmullRomClosed:Zv,curveCatmullRomOpen:Kv,curveCatmullRom:ug,curveLinear:Va,curveLinearClosed:Qv,curveMonotoneX:yg,curveMonotoneY:xg,curveNatural:_g,curveStep:Cg,curveStepAfter:kg,curveStepBefore:wg},LM=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,BM=g(function(e,t){const r=Pg(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);wa(o),i=Ht(i,[...o])}else i=r.args;if(!i)return;let n=Ml(e,t);const a="config";return i[a]!==void 0&&(n==="flowchart-v2"&&(n="flowchart"),i[n]=i[a],delete i[a]),i},"detectInit"),Pg=g(function(e,t=null){var r,i;try{const n=new RegExp(`[%]{2}(?![{]${LM.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(n,"").replace(/'/gm,'"'),R.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let a;const o=[];for(;(a=cn.exec(e))!==null;)if(a.index===cn.lastIndex&&cn.lastIndex++,a&&!t||t&&((r=a[1])!=null&&r.match(t))||t&&((i=a[2])!=null&&i.match(t))){const s=a[1]?a[1]:a[2],c=a[3]?a[3].trim():a[4]?JSON.parse(a[4].trim()):null;o.push({type:s,args:c})}return o.length===0?{type:e,args:null}:o.length===1?o[0]:o}catch(n){return R.error(`ERROR: ${n.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),EM=g(function(e){return e.replace(cn,"")},"removeDirectives"),FM=g(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function cc(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return AM[r]??t}g(cc,"interpolateToCurve");function Ng(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?K1.sanitizeUrl(r):r}g(Ng,"formatUrl");var $M=g((e,...t)=>{const r=e.split("."),i=r.length-1,n=r[i];let a=window;for(let o=0;o<i;o++)if(a=a[r[o]],!a){R.error(`Function name: ${e} not found in window`);return}a[n](...t)},"runFunc");function hc(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}g(hc,"distance");function zg(e){let t,r=0;e.forEach(n=>{r+=hc(n,t),t=n});const i=r/2;return uc(e,i)}g(zg,"traverseEdge");function Wg(e){return e.length===1?e[0]:zg(e)}g(Wg,"calcLabelPosition");var _u=g((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),uc=g((e,t)=>{let r,i=t;for(const n of e){if(r){const a=hc(n,r);if(a===0)return r;if(a<i)i-=a;else{const o=i/a;if(o<=0)return r;if(o>=1)return{x:n.x,y:n.y};if(o>0&&o<1)return{x:_u((1-o)*r.x+o*n.x,5),y:_u((1-o)*r.y+o*n.y,5)}}}r=n}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),DM=g((e,t,r)=>{R.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const n=uc(t,25),a=e?10:5,o=Math.atan2(t[0].y-n.y,t[0].x-n.x),s={x:0,y:0};return s.x=Math.sin(o)*a+(t[0].x+n.x)/2,s.y=-Math.cos(o)*a+(t[0].y+n.y)/2,s},"calcCardinalityPosition");function qg(e,t,r){const i=structuredClone(r);R.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const n=25+e,a=uc(i,n),o=10+e*.5,s=Math.atan2(i[0].y-a.y,i[0].x-a.x),c={x:0,y:0};return t==="start_left"?(c.x=Math.sin(s+Math.PI)*o+(i[0].x+a.x)/2,c.y=-Math.cos(s+Math.PI)*o+(i[0].y+a.y)/2):t==="end_right"?(c.x=Math.sin(s-Math.PI)*o+(i[0].x+a.x)/2-5,c.y=-Math.cos(s-Math.PI)*o+(i[0].y+a.y)/2-5):t==="end_left"?(c.x=Math.sin(s)*o+(i[0].x+a.x)/2-5,c.y=-Math.cos(s)*o+(i[0].y+a.y)/2-5):(c.x=Math.sin(s)*o+(i[0].x+a.x)/2,c.y=-Math.cos(s)*o+(i[0].y+a.y)/2),c}g(qg,"calcTerminalLabelPosition");function Hg(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}g(Hg,"getStylesFromArray");var Cu=0,OM=g(()=>(Cu++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Cu),"generateId");function Ug(e){let t="";const r="0123456789abcdef",i=r.length;for(let n=0;n<e;n++)t+=r.charAt(Math.floor(Math.random()*i));return t}g(Ug,"makeRandomHex");var RM=g(e=>Ug(e.length),"random"),IM=g(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),PM=g(function(e,t){const r=t.text.replace(Ai.lineBreakRegex," "),[,i]=$s(t.fontSize),n=e.append("text");n.attr("x",t.x),n.attr("y",t.y),n.style("text-anchor",t.anchor),n.style("font-family",t.fontFamily),n.style("font-size",i),n.style("font-weight",t.fontWeight),n.attr("fill",t.fill),t.class!==void 0&&n.attr("class",t.class);const a=n.append("tspan");return a.attr("x",t.x+t.textMargin*2),a.attr("fill",t.fill),a.text(r),n},"drawSimpleText"),NM=$n((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Ai.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),n=[];let a="";return i.forEach((o,s)=>{const c=er(`${o} `,r),l=er(a,r);if(c>t){const{hyphenatedStrings:f,remainingWord:d}=zM(o,t,"-",r);n.push(a,...f),a=d}else l+c>=t?(n.push(a),a=o):a=[a,o].filter(Boolean).join(" ");s+1===i.length&&n.push(a)}),n.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),zM=$n((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const n=[...e],a=[];let o="";return n.forEach((s,c)=>{const l=`${o}${s}`;if(er(l,i)>=t){const u=c+1,f=n.length===u,d=`${l}${r}`;a.push(f?l:d),o=""}else o=l}),{hyphenatedStrings:a,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function Yg(e,t){return fc(e,t).height}g(Yg,"calculateTextHeight");function er(e,t){return fc(e,t).width}g(er,"calculateTextWidth");var fc=$n((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:n=400}=t;if(!e)return{width:0,height:0};const[,a]=$s(r),o=["sans-serif",i],s=e.split(Ai.lineBreakRegex),c=[],l=pt("body");if(!l.remove)return{width:0,height:0,lineHeight:0};const h=l.append("svg");for(const f of o){let d=0;const p={width:0,height:0,lineHeight:0};for(const m of s){const y=IM();y.text=m||MM;const x=PM(h,y).style("font-size",a).style("font-weight",n).style("font-family",f),b=(x._groups||x)[0][0].getBBox();if(b.width===0&&b.height===0)throw new Error("svg element not in render tree");p.width=Math.round(Math.max(p.width,b.width)),d=Math.round(b.height),p.height+=d,p.lineHeight=Math.round(Math.max(p.lineHeight,d))}c.push(p)}h.remove();const u=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),di,WM=(di=class{constructor(t=!1,r){this.count=0,this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}},g(di,"InitIDGenerator"),di),Qn,qM=g(function(e){return Qn=Qn||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),Qn.innerHTML=e,unescape(Qn.textContent)},"entityDecode");function dc(e){return"str"in e}g(dc,"isDetailedError");var HM=g((e,t,r,i)=>{var a;if(!i)return;const n=(a=e.node())==null?void 0:a.getBBox();n&&e.append("text").text(i).attr("text-anchor","middle").attr("x",n.x+n.width/2).attr("y",-r).attr("class",t)},"insertTitle"),$s=g(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function pc(e,t){return TM({},e,t)}g(pc,"cleanAndMerge");var De={assignWithDepth:Ht,wrapLabel:NM,calculateTextHeight:Yg,calculateTextWidth:er,calculateTextDimensions:fc,cleanAndMerge:pc,detectInit:BM,detectDirective:Pg,isSubstringInArray:FM,interpolateToCurve:cc,calcLabelPosition:Wg,calcCardinalityPosition:DM,calcTerminalLabelPosition:qg,formatUrl:Ng,getStylesFromArray:Hg,generateId:OM,random:RM,runFunc:$M,entityDecode:qM,insertTitle:HM,parseFontSize:$s,InitIDGenerator:WM},UM=g(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),qr=g(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),x3=g((e,t,{counter:r=0,prefix:i,suffix:n},a)=>a||`${i?`${i}_`:""}${e}_${t}_${r}${n?`_${n}`:""}`,"getEdgeId");function re(e){return e??null}g(re,"handleUndefinedAttr");function gc(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Hr=gc();function jg(e){Hr=e}const dn={exec:()=>null};function bt(e,t=""){let r=typeof e=="string"?e:e.source;const i={replace:(n,a)=>{let o=typeof a=="string"?a:a.source;return o=o.replace(ee.caret,"$1"),r=r.replace(n,o),i},getRegex:()=>new RegExp(r,t)};return i}const ee={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},YM=/^(?:[ \t]*(?:\n|$))+/,jM=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,GM=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,On=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,VM=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,mc=/(?:[*+-]|\d{1,9}[.)])/,Gg=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vg=bt(Gg).replace(/bull/g,mc).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),XM=bt(Gg).replace(/bull/g,mc).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),yc=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ZM=/^[^\n]+/,xc=/(?!\s*\])(?:\\.|[^\[\]\\])+/,KM=bt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",xc).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),QM=bt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,mc).getRegex(),Ds="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",bc=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,JM=bt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",bc).replace("tag",Ds).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Xg=bt(yc).replace("hr",On).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ds).getRegex(),tA=bt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Xg).getRegex(),_c={blockquote:tA,code:jM,def:KM,fences:GM,heading:VM,hr:On,html:JM,lheading:Vg,list:QM,newline:YM,paragraph:Xg,table:dn,text:ZM},wu=bt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",On).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ds).getRegex(),eA={..._c,lheading:XM,table:wu,paragraph:bt(yc).replace("hr",On).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",wu).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ds).getRegex()},rA={..._c,html:bt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",bc).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:dn,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:bt(yc).replace("hr",On).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vg).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},iA=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,nA=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Zg=/^( {2,}|\\)\n(?!\s*$)/,aA=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Os=/[\p{P}\p{S}]/u,Cc=/[\s\p{P}\p{S}]/u,Kg=/[^\s\p{P}\p{S}]/u,sA=bt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Cc).getRegex(),Qg=/(?!~)[\p{P}\p{S}]/u,oA=/(?!~)[\s\p{P}\p{S}]/u,lA=/(?:[^\s\p{P}\p{S}]|~)/u,cA=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Jg=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,hA=bt(Jg,"u").replace(/punct/g,Os).getRegex(),uA=bt(Jg,"u").replace(/punct/g,Qg).getRegex(),tm="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",fA=bt(tm,"gu").replace(/notPunctSpace/g,Kg).replace(/punctSpace/g,Cc).replace(/punct/g,Os).getRegex(),dA=bt(tm,"gu").replace(/notPunctSpace/g,lA).replace(/punctSpace/g,oA).replace(/punct/g,Qg).getRegex(),pA=bt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Kg).replace(/punctSpace/g,Cc).replace(/punct/g,Os).getRegex(),gA=bt(/\\(punct)/,"gu").replace(/punct/g,Os).getRegex(),mA=bt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),yA=bt(bc).replace("(?:-->|$)","-->").getRegex(),xA=bt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",yA).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),rs=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,bA=bt(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",rs).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),em=bt(/^!?\[(label)\]\[(ref)\]/).replace("label",rs).replace("ref",xc).getRegex(),rm=bt(/^!?\[(ref)\](?:\[\])?/).replace("ref",xc).getRegex(),_A=bt("reflink|nolink(?!\\()","g").replace("reflink",em).replace("nolink",rm).getRegex(),wc={_backpedal:dn,anyPunctuation:gA,autolink:mA,blockSkip:cA,br:Zg,code:nA,del:dn,emStrongLDelim:hA,emStrongRDelimAst:fA,emStrongRDelimUnd:pA,escape:iA,link:bA,nolink:rm,punctuation:sA,reflink:em,reflinkSearch:_A,tag:xA,text:aA,url:dn},CA={...wc,link:bt(/^!?\[(label)\]\((.*?)\)/).replace("label",rs).getRegex(),reflink:bt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",rs).getRegex()},ul={...wc,emStrongRDelimAst:dA,emStrongLDelim:uA,url:bt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},wA={...ul,br:bt(Zg).replace("{2,}","*").getRegex(),text:bt(ul.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Jn={normal:_c,gfm:eA,pedantic:rA},Vi={normal:wc,gfm:ul,breaks:wA,pedantic:CA},kA={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ku=e=>kA[e];function Ee(e,t){if(t){if(ee.escapeTest.test(e))return e.replace(ee.escapeReplace,ku)}else if(ee.escapeTestNoEncode.test(e))return e.replace(ee.escapeReplaceNoEncode,ku);return e}function vu(e){try{e=encodeURI(e).replace(ee.percentDecode,"%")}catch{return null}return e}function Su(e,t){var a;const r=e.replace(ee.findPipe,(o,s,c)=>{let l=!1,h=s;for(;--h>=0&&c[h]==="\\";)l=!l;return l?"|":" |"}),i=r.split(ee.splitPipe);let n=0;if(i[0].trim()||i.shift(),i.length>0&&!((a=i.at(-1))!=null&&a.trim())&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;n<i.length;n++)i[n]=i[n].trim().replace(ee.slashPipe,"|");return i}function Xi(e,t,r){const i=e.length;if(i===0)return"";let n=0;for(;n<i&&e.charAt(i-n-1)===t;)n++;return e.slice(0,i-n)}function vA(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return r>0?-2:-1}function Tu(e,t,r,i,n){const a=t.href,o=t.title||null,s=e[1].replace(n.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){i.state.inLink=!0;const c={type:"link",raw:r,href:a,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,c}return{type:"image",raw:r,href:a,title:o,text:s}}function SA(e,t,r){const i=e.match(r.other.indentCodeCompensation);if(i===null)return t;const n=i[1];return t.split(`
`).map(a=>{const o=a.match(r.other.beginningSpace);if(o===null)return a;const[s]=o;return s.length>=n.length?a.slice(n.length):a}).join(`
`)}class is{constructor(t){Ct(this,"options");Ct(this,"rules");Ct(this,"lexer");this.options=t||Hr}space(t){const r=this.rules.block.newline.exec(t);if(r&&r[0].length>0)return{type:"space",raw:r[0]}}code(t){const r=this.rules.block.code.exec(t);if(r){const i=r[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:r[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Xi(i,`
`)}}}fences(t){const r=this.rules.block.fences.exec(t);if(r){const i=r[0],n=SA(i,r[3]||"",this.rules);return{type:"code",raw:i,lang:r[2]?r[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):r[2],text:n}}}heading(t){const r=this.rules.block.heading.exec(t);if(r){let i=r[2].trim();if(this.rules.other.endingHash.test(i)){const n=Xi(i,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(i=n.trim())}return{type:"heading",raw:r[0],depth:r[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(t){const r=this.rules.block.hr.exec(t);if(r)return{type:"hr",raw:Xi(r[0],`
`)}}blockquote(t){const r=this.rules.block.blockquote.exec(t);if(r){let i=Xi(r[0],`
`).split(`
`),n="",a="";const o=[];for(;i.length>0;){let s=!1;const c=[];let l;for(l=0;l<i.length;l++)if(this.rules.other.blockquoteStart.test(i[l]))c.push(i[l]),s=!0;else if(!s)c.push(i[l]);else break;i=i.slice(l);const h=c.join(`
`),u=h.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${h}`:h,a=a?`${a}
${u}`:u;const f=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,o,!0),this.lexer.state.top=f,i.length===0)break;const d=o.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){const p=d,m=p.raw+`
`+i.join(`
`),y=this.blockquote(m);o[o.length-1]=y,n=n.substring(0,n.length-p.raw.length)+y.raw,a=a.substring(0,a.length-p.text.length)+y.text;break}else if((d==null?void 0:d.type)==="list"){const p=d,m=p.raw+`
`+i.join(`
`),y=this.list(m);o[o.length-1]=y,n=n.substring(0,n.length-d.raw.length)+y.raw,a=a.substring(0,a.length-p.raw.length)+y.raw,i=m.substring(o.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:o,text:a}}}list(t){let r=this.rules.block.list.exec(t);if(r){let i=r[1].trim();const n=i.length>1,a={type:"list",raw:"",ordered:n,start:n?+i.slice(0,-1):"",loose:!1,items:[]};i=n?`\\d{1,9}\\${i.slice(-1)}`:`\\${i}`,this.options.pedantic&&(i=n?i:"[*+-]");const o=this.rules.other.listItemRegex(i);let s=!1;for(;t;){let l=!1,h="",u="";if(!(r=o.exec(t))||this.rules.block.hr.test(t))break;h=r[0],t=t.substring(h.length);let f=r[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,b=>" ".repeat(3*b.length)),d=t.split(`
`,1)[0],p=!f.trim(),m=0;if(this.options.pedantic?(m=2,u=f.trimStart()):p?m=r[1].length+1:(m=r[2].search(this.rules.other.nonSpaceChar),m=m>4?1:m,u=f.slice(m),m+=r[1].length),p&&this.rules.other.blankLine.test(d)&&(h+=d+`
`,t=t.substring(d.length+1),l=!0),!l){const b=this.rules.other.nextBulletRegex(m),C=this.rules.other.hrRegex(m),v=this.rules.other.fencesBeginRegex(m),k=this.rules.other.headingBeginRegex(m),_=this.rules.other.htmlBeginRegex(m);for(;t;){const w=t.split(`
`,1)[0];let O;if(d=w,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),O=d):O=d.replace(this.rules.other.tabCharGlobal,"    "),v.test(d)||k.test(d)||_.test(d)||b.test(d)||C.test(d))break;if(O.search(this.rules.other.nonSpaceChar)>=m||!d.trim())u+=`
`+O.slice(m);else{if(p||f.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||v.test(f)||k.test(f)||C.test(f))break;u+=`
`+d}!p&&!d.trim()&&(p=!0),h+=w+`
`,t=t.substring(w.length+1),f=O.slice(m)}}a.loose||(s?a.loose=!0:this.rules.other.doubleBlankLine.test(h)&&(s=!0));let y=null,x;this.options.gfm&&(y=this.rules.other.listIsTask.exec(u),y&&(x=y[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),a.items.push({type:"list_item",raw:h,task:!!y,checked:x,loose:!1,text:u,tokens:[]}),a.raw+=h}const c=a.items.at(-1);if(c)c.raw=c.raw.trimEnd(),c.text=c.text.trimEnd();else return;a.raw=a.raw.trimEnd();for(let l=0;l<a.items.length;l++)if(this.lexer.state.top=!1,a.items[l].tokens=this.lexer.blockTokens(a.items[l].text,[]),!a.loose){const h=a.items[l].tokens.filter(f=>f.type==="space"),u=h.length>0&&h.some(f=>this.rules.other.anyLine.test(f.raw));a.loose=u}if(a.loose)for(let l=0;l<a.items.length;l++)a.items[l].loose=!0;return a}}html(t){const r=this.rules.block.html.exec(t);if(r)return{type:"html",block:!0,raw:r[0],pre:r[1]==="pre"||r[1]==="script"||r[1]==="style",text:r[0]}}def(t){const r=this.rules.block.def.exec(t);if(r){const i=r[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=r[2]?r[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=r[3]?r[3].substring(1,r[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):r[3];return{type:"def",tag:i,raw:r[0],href:n,title:a}}}table(t){var s;const r=this.rules.block.table.exec(t);if(!r||!this.rules.other.tableDelimiter.test(r[2]))return;const i=Su(r[1]),n=r[2].replace(this.rules.other.tableAlignChars,"").split("|"),a=(s=r[3])!=null&&s.trim()?r[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],o={type:"table",raw:r[0],header:[],align:[],rows:[]};if(i.length===n.length){for(const c of n)this.rules.other.tableAlignRight.test(c)?o.align.push("right"):this.rules.other.tableAlignCenter.test(c)?o.align.push("center"):this.rules.other.tableAlignLeft.test(c)?o.align.push("left"):o.align.push(null);for(let c=0;c<i.length;c++)o.header.push({text:i[c],tokens:this.lexer.inline(i[c]),header:!0,align:o.align[c]});for(const c of a)o.rows.push(Su(c,o.header.length).map((l,h)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:o.align[h]})));return o}}lheading(t){const r=this.rules.block.lheading.exec(t);if(r)return{type:"heading",raw:r[0],depth:r[2].charAt(0)==="="?1:2,text:r[1],tokens:this.lexer.inline(r[1])}}paragraph(t){const r=this.rules.block.paragraph.exec(t);if(r){const i=r[1].charAt(r[1].length-1)===`
`?r[1].slice(0,-1):r[1];return{type:"paragraph",raw:r[0],text:i,tokens:this.lexer.inline(i)}}}text(t){const r=this.rules.block.text.exec(t);if(r)return{type:"text",raw:r[0],text:r[0],tokens:this.lexer.inline(r[0])}}escape(t){const r=this.rules.inline.escape.exec(t);if(r)return{type:"escape",raw:r[0],text:r[1]}}tag(t){const r=this.rules.inline.tag.exec(t);if(r)return!this.lexer.state.inLink&&this.rules.other.startATag.test(r[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(r[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(r[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(r[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:r[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:r[0]}}link(t){const r=this.rules.inline.link.exec(t);if(r){const i=r[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(i)){if(!this.rules.other.endAngleBracket.test(i))return;const o=Xi(i.slice(0,-1),"\\");if((i.length-o.length)%2===0)return}else{const o=vA(r[2],"()");if(o===-2)return;if(o>-1){const c=(r[0].indexOf("!")===0?5:4)+r[1].length+o;r[2]=r[2].substring(0,o),r[0]=r[0].substring(0,c).trim(),r[3]=""}}let n=r[2],a="";if(this.options.pedantic){const o=this.rules.other.pedanticHrefTitle.exec(n);o&&(n=o[1],a=o[3])}else a=r[3]?r[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(i)?n=n.slice(1):n=n.slice(1,-1)),Tu(r,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},r[0],this.lexer,this.rules)}}reflink(t,r){let i;if((i=this.rules.inline.reflink.exec(t))||(i=this.rules.inline.nolink.exec(t))){const n=(i[2]||i[1]).replace(this.rules.other.multipleSpaceGlobal," "),a=r[n.toLowerCase()];if(!a){const o=i[0].charAt(0);return{type:"text",raw:o,text:o}}return Tu(i,a,i[0],this.lexer,this.rules)}}emStrong(t,r,i=""){let n=this.rules.inline.emStrongLDelim.exec(t);if(!n||n[3]&&i.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!i||this.rules.inline.punctuation.exec(i)){const o=[...n[0]].length-1;let s,c,l=o,h=0;const u=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,r=r.slice(-1*t.length+o);(n=u.exec(r))!=null;){if(s=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!s)continue;if(c=[...s].length,n[3]||n[4]){l+=c;continue}else if((n[5]||n[6])&&o%3&&!((o+c)%3)){h+=c;continue}if(l-=c,l>0)continue;c=Math.min(c,c+l+h);const f=[...n[0]][0].length,d=t.slice(0,o+n.index+f+c);if(Math.min(o,c)%2){const m=d.slice(1,-1);return{type:"em",raw:d,text:m,tokens:this.lexer.inlineTokens(m)}}const p=d.slice(2,-2);return{type:"strong",raw:d,text:p,tokens:this.lexer.inlineTokens(p)}}}}codespan(t){const r=this.rules.inline.code.exec(t);if(r){let i=r[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(i),a=this.rules.other.startingSpaceChar.test(i)&&this.rules.other.endingSpaceChar.test(i);return n&&a&&(i=i.substring(1,i.length-1)),{type:"codespan",raw:r[0],text:i}}}br(t){const r=this.rules.inline.br.exec(t);if(r)return{type:"br",raw:r[0]}}del(t){const r=this.rules.inline.del.exec(t);if(r)return{type:"del",raw:r[0],text:r[2],tokens:this.lexer.inlineTokens(r[2])}}autolink(t){const r=this.rules.inline.autolink.exec(t);if(r){let i,n;return r[2]==="@"?(i=r[1],n="mailto:"+i):(i=r[1],n=i),{type:"link",raw:r[0],text:i,href:n,tokens:[{type:"text",raw:i,text:i}]}}}url(t){var i;let r;if(r=this.rules.inline.url.exec(t)){let n,a;if(r[2]==="@")n=r[0],a="mailto:"+n;else{let o;do o=r[0],r[0]=((i=this.rules.inline._backpedal.exec(r[0]))==null?void 0:i[0])??"";while(o!==r[0]);n=r[0],r[1]==="www."?a="http://"+r[0]:a=r[0]}return{type:"link",raw:r[0],text:n,href:a,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){const r=this.rules.inline.text.exec(t);if(r){const i=this.lexer.state.inRawBlock;return{type:"text",raw:r[0],text:r[0],escaped:i}}}}class ye{constructor(t){Ct(this,"tokens");Ct(this,"options");Ct(this,"state");Ct(this,"tokenizer");Ct(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Hr,this.options.tokenizer=this.options.tokenizer||new is,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const r={other:ee,block:Jn.normal,inline:Vi.normal};this.options.pedantic?(r.block=Jn.pedantic,r.inline=Vi.pedantic):this.options.gfm&&(r.block=Jn.gfm,this.options.breaks?r.inline=Vi.breaks:r.inline=Vi.gfm),this.tokenizer.rules=r}static get rules(){return{block:Jn,inline:Vi}}static lex(t,r){return new ye(r).lex(t)}static lexInline(t,r){return new ye(r).inlineTokens(t)}lex(t){t=t.replace(ee.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){const i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){var n,a,o;for(this.options.pedantic&&(t=t.replace(ee.tabCharGlobal,"    ").replace(ee.spaceLine,""));t;){let s;if((a=(n=this.options.extensions)==null?void 0:n.block)!=null&&a.some(l=>(s=l.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);const l=r.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:r.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),r.push(s);continue}let c=t;if((o=this.options.extensions)!=null&&o.startBlock){let l=1/0;const h=t.slice(1);let u;this.options.extensions.startBlock.forEach(f=>{u=f.call({lexer:this},h),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(c=t.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(c))){const l=r.at(-1);i&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s),i=c.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){var s,c,l;let i=t,n=null;if(this.tokens.links){const h=Object.keys(this.tokens.links);if(h.length>0)for(;(n=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)h.includes(n[0].slice(n[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(n=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,n.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(n=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,o="";for(;t;){a||(o=""),a=!1;let h;if((c=(s=this.options.extensions)==null?void 0:s.inline)!=null&&c.some(f=>(h=f.call({lexer:this},t,r))?(t=t.substring(h.raw.length),r.push(h),!0):!1))continue;if(h=this.tokenizer.escape(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.tag(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.link(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(h.raw.length);const f=r.at(-1);h.type==="text"&&(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(h=this.tokenizer.emStrong(t,i,o)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.codespan(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.br(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.del(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.autolink(t)){t=t.substring(h.raw.length),r.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(t))){t=t.substring(h.raw.length),r.push(h);continue}let u=t;if((l=this.options.extensions)!=null&&l.startInline){let f=1/0;const d=t.slice(1);let p;this.options.extensions.startInline.forEach(m=>{p=m.call({lexer:this},d),typeof p=="number"&&p>=0&&(f=Math.min(f,p))}),f<1/0&&f>=0&&(u=t.substring(0,f+1))}if(h=this.tokenizer.inlineText(u)){t=t.substring(h.raw.length),h.raw.slice(-1)!=="_"&&(o=h.raw.slice(-1)),a=!0;const f=r.at(-1);(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(t){const f="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(f);break}else throw new Error(f)}}return r}}class ns{constructor(t){Ct(this,"options");Ct(this,"parser");this.options=t||Hr}space(t){return""}code({text:t,lang:r,escaped:i}){var o;const n=(o=(r||"").match(ee.notSpaceStart))==null?void 0:o[0],a=t.replace(ee.endingNewline,"")+`
`;return n?'<pre><code class="language-'+Ee(n)+'">'+(i?a:Ee(a,!0))+`</code></pre>
`:"<pre><code>"+(i?a:Ee(a,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:r}){return`<h${r}>${this.parser.parseInline(t)}</h${r}>
`}hr(t){return`<hr>
`}list(t){const r=t.ordered,i=t.start;let n="";for(let s=0;s<t.items.length;s++){const c=t.items[s];n+=this.listitem(c)}const a=r?"ol":"ul",o=r&&i!==1?' start="'+i+'"':"";return"<"+a+o+`>
`+n+"</"+a+`>
`}listitem(t){var i;let r="";if(t.task){const n=this.checkbox({checked:!!t.checked});t.loose?((i=t.tokens[0])==null?void 0:i.type)==="paragraph"?(t.tokens[0].text=n+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=n+" "+Ee(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):r+=n+" "}return r+=this.parser.parse(t.tokens,!!t.loose),`<li>${r}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let r="",i="";for(let a=0;a<t.header.length;a++)i+=this.tablecell(t.header[a]);r+=this.tablerow({text:i});let n="";for(let a=0;a<t.rows.length;a++){const o=t.rows[a];i="";for(let s=0;s<o.length;s++)i+=this.tablecell(o[s]);n+=this.tablerow({text:i})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+r+`</thead>
`+n+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){const r=this.parser.parseInline(t.tokens),i=t.header?"th":"td";return(t.align?`<${i} align="${t.align}">`:`<${i}>`)+r+`</${i}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${Ee(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:r,tokens:i}){const n=this.parser.parseInline(i),a=vu(t);if(a===null)return n;t=a;let o='<a href="'+t+'"';return r&&(o+=' title="'+Ee(r)+'"'),o+=">"+n+"</a>",o}image({href:t,title:r,text:i}){const n=vu(t);if(n===null)return Ee(i);t=n;let a=`<img src="${t}" alt="${i}"`;return r&&(a+=` title="${Ee(r)}"`),a+=">",a}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:Ee(t.text)}}class kc{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}}class xe{constructor(t){Ct(this,"options");Ct(this,"renderer");Ct(this,"textRenderer");this.options=t||Hr,this.options.renderer=this.options.renderer||new ns,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new kc}static parse(t,r){return new xe(r).parse(t)}static parseInline(t,r){return new xe(r).parseInline(t)}parse(t,r=!0){var n,a;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((a=(n=this.options.extensions)==null?void 0:n.renderers)!=null&&a[s.type]){const l=s,h=this.options.extensions.renderers[l.type].call({parser:this},l);if(h!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){i+=h||"";continue}}const c=s;switch(c.type){case"space":{i+=this.renderer.space(c);continue}case"hr":{i+=this.renderer.hr(c);continue}case"heading":{i+=this.renderer.heading(c);continue}case"code":{i+=this.renderer.code(c);continue}case"table":{i+=this.renderer.table(c);continue}case"blockquote":{i+=this.renderer.blockquote(c);continue}case"list":{i+=this.renderer.list(c);continue}case"html":{i+=this.renderer.html(c);continue}case"paragraph":{i+=this.renderer.paragraph(c);continue}case"text":{let l=c,h=this.renderer.text(l);for(;o+1<t.length&&t[o+1].type==="text";)l=t[++o],h+=`
`+this.renderer.text(l);r?i+=this.renderer.paragraph({type:"paragraph",raw:h,text:h,tokens:[{type:"text",raw:h,text:h,escaped:!0}]}):i+=h;continue}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}parseInline(t,r=this.renderer){var n,a;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((a=(n=this.options.extensions)==null?void 0:n.renderers)!=null&&a[s.type]){const l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=l||"";continue}}const c=s;switch(c.type){case"escape":{i+=r.text(c);break}case"html":{i+=r.html(c);break}case"link":{i+=r.link(c);break}case"image":{i+=r.image(c);break}case"strong":{i+=r.strong(c);break}case"em":{i+=r.em(c);break}case"codespan":{i+=r.codespan(c);break}case"br":{i+=r.br(c);break}case"del":{i+=r.del(c);break}case"text":{i+=r.text(c);break}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}}class pn{constructor(t){Ct(this,"options");Ct(this,"block");this.options=t||Hr}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?ye.lex:ye.lexInline}provideParser(){return this.block?xe.parse:xe.parseInline}}Ct(pn,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class TA{constructor(...t){Ct(this,"defaults",gc());Ct(this,"options",this.setOptions);Ct(this,"parse",this.parseMarkdown(!0));Ct(this,"parseInline",this.parseMarkdown(!1));Ct(this,"Parser",xe);Ct(this,"Renderer",ns);Ct(this,"TextRenderer",kc);Ct(this,"Lexer",ye);Ct(this,"Tokenizer",is);Ct(this,"Hooks",pn);this.use(...t)}walkTokens(t,r){var n,a;let i=[];for(const o of t)switch(i=i.concat(r.call(this,o)),o.type){case"table":{const s=o;for(const c of s.header)i=i.concat(this.walkTokens(c.tokens,r));for(const c of s.rows)for(const l of c)i=i.concat(this.walkTokens(l.tokens,r));break}case"list":{const s=o;i=i.concat(this.walkTokens(s.items,r));break}default:{const s=o;(a=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&a[s.type]?this.defaults.extensions.childTokens[s.type].forEach(c=>{const l=s[c].flat(1/0);i=i.concat(this.walkTokens(l,r))}):s.tokens&&(i=i.concat(this.walkTokens(s.tokens,r)))}}return i}use(...t){const r=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(i=>{const n={...i};if(n.async=this.defaults.async||n.async||!1,i.extensions&&(i.extensions.forEach(a=>{if(!a.name)throw new Error("extension name required");if("renderer"in a){const o=r.renderers[a.name];o?r.renderers[a.name]=function(...s){let c=a.renderer.apply(this,s);return c===!1&&(c=o.apply(this,s)),c}:r.renderers[a.name]=a.renderer}if("tokenizer"in a){if(!a.level||a.level!=="block"&&a.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=r[a.level];o?o.unshift(a.tokenizer):r[a.level]=[a.tokenizer],a.start&&(a.level==="block"?r.startBlock?r.startBlock.push(a.start):r.startBlock=[a.start]:a.level==="inline"&&(r.startInline?r.startInline.push(a.start):r.startInline=[a.start]))}"childTokens"in a&&a.childTokens&&(r.childTokens[a.name]=a.childTokens)}),n.extensions=r),i.renderer){const a=this.defaults.renderer||new ns(this.defaults);for(const o in i.renderer){if(!(o in a))throw new Error(`renderer '${o}' does not exist`);if(["options","parser"].includes(o))continue;const s=o,c=i.renderer[s],l=a[s];a[s]=(...h)=>{let u=c.apply(a,h);return u===!1&&(u=l.apply(a,h)),u||""}}n.renderer=a}if(i.tokenizer){const a=this.defaults.tokenizer||new is(this.defaults);for(const o in i.tokenizer){if(!(o in a))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const s=o,c=i.tokenizer[s],l=a[s];a[s]=(...h)=>{let u=c.apply(a,h);return u===!1&&(u=l.apply(a,h)),u}}n.tokenizer=a}if(i.hooks){const a=this.defaults.hooks||new pn;for(const o in i.hooks){if(!(o in a))throw new Error(`hook '${o}' does not exist`);if(["options","block"].includes(o))continue;const s=o,c=i.hooks[s],l=a[s];pn.passThroughHooks.has(o)?a[s]=h=>{if(this.defaults.async)return Promise.resolve(c.call(a,h)).then(f=>l.call(a,f));const u=c.call(a,h);return l.call(a,u)}:a[s]=(...h)=>{let u=c.apply(a,h);return u===!1&&(u=l.apply(a,h)),u}}n.hooks=a}if(i.walkTokens){const a=this.defaults.walkTokens,o=i.walkTokens;n.walkTokens=function(s){let c=[];return c.push(o.call(this,s)),a&&(c=c.concat(a.call(this,s))),c}}this.defaults={...this.defaults,...n}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,r){return ye.lex(t,r??this.defaults)}parser(t,r){return xe.parse(t,r??this.defaults)}parseMarkdown(t){return(i,n)=>{const a={...n},o={...this.defaults,...a},s=this.onError(!!o.silent,!!o.async);if(this.defaults.async===!0&&a.async===!1)return s(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof i>"u"||i===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof i!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(i)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=t);const c=o.hooks?o.hooks.provideLexer():t?ye.lex:ye.lexInline,l=o.hooks?o.hooks.provideParser():t?xe.parse:xe.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(i):i).then(h=>c(h,o)).then(h=>o.hooks?o.hooks.processAllTokens(h):h).then(h=>o.walkTokens?Promise.all(this.walkTokens(h,o.walkTokens)).then(()=>h):h).then(h=>l(h,o)).then(h=>o.hooks?o.hooks.postprocess(h):h).catch(s);try{o.hooks&&(i=o.hooks.preprocess(i));let h=c(i,o);o.hooks&&(h=o.hooks.processAllTokens(h)),o.walkTokens&&this.walkTokens(h,o.walkTokens);let u=l(h,o);return o.hooks&&(u=o.hooks.postprocess(u)),u}catch(h){return s(h)}}}onError(t,r){return i=>{if(i.message+=`
Please report this to https://github.com/markedjs/marked.`,t){const n="<p>An error occurred:</p><pre>"+Ee(i.message+"",!0)+"</pre>";return r?Promise.resolve(n):n}if(r)return Promise.reject(i);throw i}}}const Dr=new TA;function xt(e,t){return Dr.parse(e,t)}xt.options=xt.setOptions=function(e){return Dr.setOptions(e),xt.defaults=Dr.defaults,jg(xt.defaults),xt};xt.getDefaults=gc;xt.defaults=Hr;xt.use=function(...e){return Dr.use(...e),xt.defaults=Dr.defaults,jg(xt.defaults),xt};xt.walkTokens=function(e,t){return Dr.walkTokens(e,t)};xt.parseInline=Dr.parseInline;xt.Parser=xe;xt.parser=xe.parse;xt.Renderer=ns;xt.TextRenderer=kc;xt.Lexer=ye;xt.lexer=ye.lex;xt.Tokenizer=is;xt.Hooks=pn;xt.parse=xt;xt.options;xt.setOptions;xt.use;xt.walkTokens;xt.parseInline;xe.parse;ye.lex;function im(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var n=i.reduce(function(s,c){var l=c.match(/\n([\t ]+|(?!\s).)/g);return l?s.concat(l.map(function(h){var u,f;return(f=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&f!==void 0?f:0})):s},[]);if(n.length){var a=new RegExp(`
[	 ]{`+Math.min.apply(Math,n)+"}","g");i=i.map(function(s){return s.replace(a,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,c){var l=o.match(/(?:^|\n)( *)$/),h=l?l[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(f,d){return d===0?f:""+h+f}).join(`
`)),o+=u+i[c+1]}),o}function nm(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),n=im(i);return t===!1?n.replace(/ /g,"&nbsp;"):n}g(nm,"preprocessMarkdown");function am(e,t={}){const r=nm(e,t),i=xt.lexer(r),n=[[]];let a=0;function o(s,c="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(a++,n.push([])),h.split(" ").forEach(f=>{f=f.replace(/&#39;/g,"'"),f&&n[a].push({content:f,type:c})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(l=>{o(l,s.type)}):s.type==="html"&&n[a].push({content:s.text,type:"normal"})}return g(o,"processNode"),i.forEach(s=>{var c;s.type==="paragraph"?(c=s.tokens)==null||c.forEach(l=>{o(l)}):s.type==="html"&&n[a].push({content:s.text,type:"normal"})}),n}g(am,"markdownToLines");function sm(e,{markdownAutoWrap:t}={}){const r=xt.lexer(e);function i(n){var a,o,s;return n.type==="text"?t===!1?n.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):n.text.replace(/\n */g,"<br/>"):n.type==="strong"?`<strong>${(a=n.tokens)==null?void 0:a.map(i).join("")}</strong>`:n.type==="em"?`<em>${(o=n.tokens)==null?void 0:o.map(i).join("")}</em>`:n.type==="paragraph"?`<p>${(s=n.tokens)==null?void 0:s.map(i).join("")}</p>`:n.type==="space"?"":n.type==="html"?`${n.text}`:n.type==="escape"?n.text:`Unsupported markdown: ${n.type}`}return g(i,"output"),r.map(i).join("")}g(sm,"markdownToHTML");function om(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}g(om,"splitTextToChars");function lm(e,t){const r=om(t.content);return vc(e,[],r,t.type)}g(lm,"splitWordToFitWidth");function vc(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[n,...a]=r,o=[...t,n];return e([{content:o.join(""),type:i}])?vc(e,o,a,i):(t.length===0&&n&&(t.push(n),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}g(vc,"splitWordToFitWidthRecursion");function cm(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return as(e,t)}g(cm,"splitLineToFitWidth");function as(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let n="";e[0].content===" "&&(n=" ",e.shift());const a=e.shift()??{content:" ",type:"normal"},o=[...i];if(n!==""&&o.push({content:n,type:"normal"}),o.push(a),t(o))return as(e,t,r,o);if(i.length>0)r.push(i),e.unshift(a);else if(a.content){const[s,c]=lm(t,a);r.push([s]),c.content&&e.unshift(c)}return as(e,t,r)}g(as,"splitLineToFitWidthRecursion");function fl(e,t){t&&e.attr("style",t)}g(fl,"applyStyle");async function hm(e,t,r,i,n=!1){const a=e.append("foreignObject");a.attr("width",`${10*r}px`),a.attr("height",`${10*r}px`);const o=a.append("xhtml:div");let s=t.label;t.label&&xi(t.label)&&(s=await Al(t.label.replace(Ai.lineBreakRegex,`
`),yt()));const c=t.isNode?"nodeLabel":"edgeLabel",l=o.append("span");l.html(s),fl(l,t.labelStyle),l.attr("class",`${c} ${i}`),fl(o,t.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",r+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),n&&o.attr("class","labelBkg");let h=o.node().getBoundingClientRect();return h.width===r&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",r+"px"),h=o.node().getBoundingClientRect()),a.node()}g(hm,"addHtmlSpan");function Rs(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}g(Rs,"createTspan");function um(e,t,r){const i=e.append("text"),n=Rs(i,1,t);Is(n,r);const a=n.node().getComputedTextLength();return i.remove(),a}g(um,"computeWidthOfText");function MA(e,t,r){var o;const i=e.append("text"),n=Rs(i,1,t);Is(n,[{content:r,type:"normal"}]);const a=(o=n.node())==null?void 0:o.getBoundingClientRect();return a&&i.remove(),a}g(MA,"computeDimensionOfText");function fm(e,t,r,i=!1){const a=t.append("g"),o=a.insert("rect").attr("class","background").attr("style","stroke: none"),s=a.append("text").attr("y","-10.1");let c=0;for(const l of r){const h=g(f=>um(a,1.1,f)<=e,"checkWidth"),u=h(l)?[l]:cm(l,h);for(const f of u){const d=Rs(s,c,1.1);Is(d,f),c++}}if(i){const l=s.node().getBBox(),h=2;return o.attr("x",l.x-h).attr("y",l.y-h).attr("width",l.width+2*h).attr("height",l.height+2*h),a.node()}else return s.node()}g(fm,"createFormattedText");function Is(e,t){e.text(""),t.forEach((r,i)=>{const n=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?n.text(r.content):n.text(" "+r.content)})}g(Is,"updateTextContentAndStyles");function dm(e){return e.replace(/fa[bklrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`)}g(dm,"replaceIconSubstring");var dr=g(async(e,t="",{style:r="",isTitle:i=!1,classes:n="",useHtmlLabels:a=!0,isNode:o=!0,width:s=200,addSvgBackground:c=!1}={},l)=>{if(R.debug("XYZ createText",t,r,i,n,a,o,"addSvgBackground: ",c),a){const h=sm(t,l),u=dm(qr(h)),f=t.replace(/\\\\/g,"\\"),d={isNode:o,label:xi(t)?f:u,labelStyle:r.replace("fill:","color:")};return await hm(e,d,s,n,c)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=am(h.replace("<br>","<br/>"),l),f=fm(s,e,u,t?c:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");pt(f).attr("style",d)}else{const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");pt(f).select("rect").attr("style",d.replace(/background:/g,"fill:"));const p=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");pt(f).select("text").attr("style",p)}return f}},"createText");function bo(e,t,r){if(e&&e.length){const[i,n]=t,a=Math.PI/180*r,o=Math.cos(a),s=Math.sin(a);for(const c of e){const[l,h]=c;c[0]=(l-i)*o-(h-n)*s+i,c[1]=(l-i)*s+(h-n)*o+n}}}function AA(e,t){return e[0]===t[0]&&e[1]===t[1]}function LA(e,t,r,i=1){const n=r,a=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(n)for(const l of o)bo(l,s,n);const c=function(l,h,u){const f=[];for(const b of l){const C=[...b];AA(C[0],C[C.length-1])||C.push([C[0][0],C[0][1]]),C.length>2&&f.push(C)}const d=[];h=Math.max(h,.1);const p=[];for(const b of f)for(let C=0;C<b.length-1;C++){const v=b[C],k=b[C+1];if(v[1]!==k[1]){const _=Math.min(v[1],k[1]);p.push({ymin:_,ymax:Math.max(v[1],k[1]),x:_===v[1]?v[0]:k[0],islope:(k[0]-v[0])/(k[1]-v[1])})}}if(p.sort((b,C)=>b.ymin<C.ymin?-1:b.ymin>C.ymin?1:b.x<C.x?-1:b.x>C.x?1:b.ymax===C.ymax?0:(b.ymax-C.ymax)/Math.abs(b.ymax-C.ymax)),!p.length)return d;let m=[],y=p[0].ymin,x=0;for(;m.length||p.length;){if(p.length){let b=-1;for(let C=0;C<p.length&&!(p[C].ymin>y);C++)b=C;p.splice(0,b+1).forEach(C=>{m.push({s:y,edge:C})})}if(m=m.filter(b=>!(b.edge.ymax<=y)),m.sort((b,C)=>b.edge.x===C.edge.x?0:(b.edge.x-C.edge.x)/Math.abs(b.edge.x-C.edge.x)),(u!==1||x%h==0)&&m.length>1)for(let b=0;b<m.length;b+=2){const C=b+1;if(C>=m.length)break;const v=m[b].edge,k=m[C].edge;d.push([[Math.round(v.x),y],[Math.round(k.x),y]])}y+=u,m.forEach(b=>{b.edge.x=b.edge.x+u*b.edge.islope}),x++}return d}(o,a,i);if(n){for(const l of o)bo(l,s,-n);(function(l,h,u){const f=[];l.forEach(d=>f.push(...d)),bo(f,h,u)})(c,s,-n)}return c}function Rn(e,t){var r;const i=t.hachureAngle+90;let n=t.hachureGap;n<0&&(n=4*t.strokeWidth),n=Math.round(Math.max(n,.1));let a=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(a=n),LA(e,n,i,a||1)}class Sc{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=Rn(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const n of t)i.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],r));return i}}function Ps(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class BA extends Sc{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const n=Rn(t,Object.assign({},r,{hachureGap:i})),a=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(a),c=.5*i*Math.sin(a);for(const[l,h]of n)Ps([l,h])&&o.push([[l[0]-s,l[1]+c],[...h]],[[l[0]+s,l[1]-c],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class EA extends Sc{fillPolygons(t,r){const i=this._fillPolygons(t,r),n=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),a=this._fillPolygons(t,n);return i.ops=i.ops.concat(a.ops),i}}class FA{constructor(t){this.helper=t}fillPolygons(t,r){const i=Rn(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let n=r.hachureGap;n<0&&(n=4*r.strokeWidth),n=Math.max(n,.1);let a=r.fillWeight;a<0&&(a=r.strokeWidth/2);const o=n/4;for(const s of t){const c=Ps(s),l=c/n,h=Math.ceil(l)-1,u=c-h*n,f=(s[0][0]+s[1][0])/2-n/4,d=Math.min(s[0][1],s[1][1]);for(let p=0;p<h;p++){const m=d+u+p*n,y=f-o+2*Math.random()*o,x=m-o+2*Math.random()*o,b=this.helper.ellipse(y,x,a,a,r);i.push(...b.ops)}}return{type:"fillSketch",ops:i}}}class $A{constructor(t){this.helper=t}fillPolygons(t,r){const i=Rn(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,n=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,a=[];return t.forEach(o=>{const s=Ps(o),c=Math.floor(s/(i+n)),l=(s+n-c*(i+n))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const f=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let d=0;d<c;d++){const p=d*(i+n),m=p+i,y=[h[0]+p*Math.cos(f)+l*Math.cos(f),h[1]+p*Math.sin(f)+l*Math.sin(f)],x=[h[0]+m*Math.cos(f)+l*Math.cos(f),h[1]+m*Math.sin(f)+l*Math.sin(f)];a.push(...this.helper.doubleLineOps(y[0],y[1],x[0],x[1],r))}}),a}}class DA{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,n=r.zigzagOffset<0?i:r.zigzagOffset,a=Rn(t,r=Object.assign({},r,{hachureGap:i+n}));return{type:"fillSketch",ops:this.zigzagLines(a,n,r)}}zigzagLines(t,r,i){const n=[];return t.forEach(a=>{const o=Ps(a),s=Math.round(o/(2*r));let c=a[0],l=a[1];c[0]>l[0]&&(c=a[1],l=a[0]);const h=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let u=0;u<s;u++){const f=2*u*r,d=2*(u+1)*r,p=Math.sqrt(2*Math.pow(r,2)),m=[c[0]+f*Math.cos(h),c[1]+f*Math.sin(h)],y=[c[0]+d*Math.cos(h),c[1]+d*Math.sin(h)],x=[m[0]+p*Math.cos(h+Math.PI/4),m[1]+p*Math.sin(h+Math.PI/4)];n.push(...this.helper.doubleLineOps(m[0],m[1],x[0],x[1],i),...this.helper.doubleLineOps(x[0],x[1],y[0],y[1],i))}}),n}}const oe={};class OA{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const RA=0,_o=1,Mu=2,ta={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function Co(e,t){return e.type===t}function Tc(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:RA,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:_o,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:Mu,text:""},s}(e);let i="BOD",n=0,a=r[n];for(;!Co(a,Mu);){let o=0;const s=[];if(i==="BOD"){if(a.text!=="M"&&a.text!=="m")return Tc("M0,0"+e);n++,o=ta[a.text],i=a.text}else Co(a,_o)?o=ta[i]:(n++,o=ta[a.text],i=a.text);if(!(n+o<r.length))throw new Error("Path data ended short");for(let c=n;c<n+o;c++){const l=r[c];if(!Co(l,_o))throw new Error("Param not a number: "+i+","+l.text);s[s.length]=+l.text}if(typeof ta[i]!="number")throw new Error("Bad segment: "+i);{const c={key:i,data:s};t.push(c),n+=o,a=r[n],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function pm(e){let t=0,r=0,i=0,n=0;const a=[];for(const{key:o,data:s}of e)switch(o){case"M":a.push({key:"M",data:[...s]}),[t,r]=s,[i,n]=s;break;case"m":t+=s[0],r+=s[1],a.push({key:"M",data:[t,r]}),i=t,n=r;break;case"L":a.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],a.push({key:"L",data:[t,r]});break;case"C":a.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"C",data:c}),t=c[4],r=c[5];break}case"Q":a.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"Q",data:c}),t=c[2],r=c[3];break}case"A":a.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],a.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":a.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],a.push({key:"H",data:[t]});break;case"V":a.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],a.push({key:"V",data:[r]});break;case"S":a.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"S",data:c}),t=c[2],r=c[3];break}case"T":a.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],a.push({key:"T",data:[t,r]});break;case"Z":case"z":a.push({key:"Z",data:[]}),t=i,r=n}return a}function gm(e){const t=[];let r="",i=0,n=0,a=0,o=0,s=0,c=0;for(const{key:l,data:h}of e){switch(l){case"M":t.push({key:"M",data:[...h]}),[i,n]=h,[a,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],n=h[5],s=h[2],c=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,n]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,n]});break;case"V":n=h[0],t.push({key:"L",data:[i,n]});break;case"S":{let u=0,f=0;r==="C"||r==="S"?(u=i+(i-s),f=n+(n-c)):(u=i,f=n),t.push({key:"C",data:[u,f,...h]}),s=h[0],c=h[1],i=h[2],n=h[3];break}case"T":{const[u,f]=h;let d=0,p=0;r==="Q"||r==="T"?(d=i+(i-s),p=n+(n-c)):(d=i,p=n);const m=i+2*(d-i)/3,y=n+2*(p-n)/3,x=u+2*(d-u)/3,b=f+2*(p-f)/3;t.push({key:"C",data:[m,y,x,b,u,f]}),s=d,c=p,i=u,n=f;break}case"Q":{const[u,f,d,p]=h,m=i+2*(u-i)/3,y=n+2*(f-n)/3,x=d+2*(u-d)/3,b=p+2*(f-p)/3;t.push({key:"C",data:[m,y,x,b,d,p]}),s=u,c=f,i=d,n=p;break}case"A":{const u=Math.abs(h[0]),f=Math.abs(h[1]),d=h[2],p=h[3],m=h[4],y=h[5],x=h[6];u===0||f===0?(t.push({key:"C",data:[i,n,y,x,y,x]}),i=y,n=x):(i!==y||n!==x)&&(mm(i,n,y,x,u,f,d,p,m).forEach(function(b){t.push({key:"C",data:b})}),i=y,n=x);break}case"Z":t.push({key:"Z",data:[]}),i=a,n=o}r=l}return t}function Zi(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function mm(e,t,r,i,n,a,o,s,c,l){const h=(u=o,Math.PI*u/180);var u;let f=[],d=0,p=0,m=0,y=0;if(l)[d,p,m,y]=l;else{[e,t]=Zi(e,t,-h),[r,i]=Zi(r,i,-h);const E=(e-r)/2,A=(t-i)/2;let F=E*E/(n*n)+A*A/(a*a);F>1&&(F=Math.sqrt(F),n*=F,a*=F);const L=n*n,D=a*a,B=L*D-L*A*A-D*E*E,W=L*A*A+D*E*E,U=(s===c?-1:1)*Math.sqrt(Math.abs(B/W));m=U*n*A/a+(e+r)/2,y=U*-a*E/n+(t+i)/2,d=Math.asin(parseFloat(((t-y)/a).toFixed(9))),p=Math.asin(parseFloat(((i-y)/a).toFixed(9))),e<m&&(d=Math.PI-d),r<m&&(p=Math.PI-p),d<0&&(d=2*Math.PI+d),p<0&&(p=2*Math.PI+p),c&&d>p&&(d-=2*Math.PI),!c&&p>d&&(p-=2*Math.PI)}let x=p-d;if(Math.abs(x)>120*Math.PI/180){const E=p,A=r,F=i;p=c&&p>d?d+120*Math.PI/180*1:d+120*Math.PI/180*-1,f=mm(r=m+n*Math.cos(p),i=y+a*Math.sin(p),A,F,n,a,o,0,c,[p,E,m,y])}x=p-d;const b=Math.cos(d),C=Math.sin(d),v=Math.cos(p),k=Math.sin(p),_=Math.tan(x/4),w=4/3*n*_,O=4/3*a*_,N=[e,t],$=[e+w*C,t-O*b],S=[r+w*k,i-O*v],I=[r,i];if($[0]=2*N[0]-$[0],$[1]=2*N[1]-$[1],l)return[$,S,I].concat(f);{f=[$,S,I].concat(f);const E=[];for(let A=0;A<f.length;A+=3){const F=Zi(f[A][0],f[A][1],h),L=Zi(f[A+1][0],f[A+1][1],h),D=Zi(f[A+2][0],f[A+2][1],h);E.push([F[0],F[1],L[0],L[1],D[0],D[1]])}return E}}const IA={randOffset:function(e,t){return st(e,t)},randOffsetWithRange:function(e,t,r){return ss(e,t,r)},ellipse:function(e,t,r,i,n){const a=xm(r,i,n);return dl(e,t,n,a).opset},doubleLineOps:function(e,t,r,i,n){return hr(e,t,r,i,n,!0)}};function ym(e,t,r,i,n){return{type:"path",ops:hr(e,t,r,i,n)}}function ya(e,t,r){const i=(e||[]).length;if(i>2){const n=[];for(let a=0;a<i-1;a++)n.push(...hr(e[a][0],e[a][1],e[a+1][0],e[a+1][1],r));return t&&n.push(...hr(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:n}}return i===2?ym(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function PA(e,t,r,i,n){return function(a,o){return ya(a,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],n)}function Au(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=ea(r[0],1*(1+.2*t.roughness),t),n=t.disableMultiStroke?[]:ea(r[0],1.5*(1+.22*t.roughness),Eu(t));for(let a=1;a<r.length;a++){const o=r[a];if(o.length){const s=ea(o,1*(1+.2*t.roughness),t),c=t.disableMultiStroke?[]:ea(o,1.5*(1+.22*t.roughness),Eu(t));for(const l of s)l.op!=="move"&&i.push(l);for(const l of c)l.op!=="move"&&n.push(l)}}return{type:"path",ops:i.concat(n)}}return{type:"path",ops:[]}}function xm(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),n=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),a=2*Math.PI/n;let o=Math.abs(e/2),s=Math.abs(t/2);const c=1-r.curveFitting;return o+=st(o*c,r),s+=st(s*c,r),{increment:a,rx:o,ry:s}}function dl(e,t,r,i){const[n,a]=Fu(i.increment,e,t,i.rx,i.ry,1,i.increment*ss(.1,ss(.4,1,r),r),r);let o=os(n,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=Fu(i.increment,e,t,i.rx,i.ry,1.5,0,r),c=os(s,null,r);o=o.concat(c)}return{estimatedPoints:a,opset:{type:"path",ops:o}}}function Lu(e,t,r,i,n,a,o,s,c){const l=e,h=t;let u=Math.abs(r/2),f=Math.abs(i/2);u+=st(.01*u,c),f+=st(.01*f,c);let d=n,p=a;for(;d<0;)d+=2*Math.PI,p+=2*Math.PI;p-d>2*Math.PI&&(d=0,p=2*Math.PI);const m=2*Math.PI/c.curveStepCount,y=Math.min(m/2,(p-d)/2),x=$u(y,l,h,u,f,d,p,1,c);if(!c.disableMultiStroke){const b=$u(y,l,h,u,f,d,p,1.5,c);x.push(...b)}return o&&(s?x.push(...hr(l,h,l+u*Math.cos(d),h+f*Math.sin(d),c),...hr(l,h,l+u*Math.cos(p),h+f*Math.sin(p),c)):x.push({op:"lineTo",data:[l,h]},{op:"lineTo",data:[l+u*Math.cos(d),h+f*Math.sin(d)]})),{type:"path",ops:x}}function Bu(e,t){const r=gm(pm(Tc(e))),i=[];let n=[0,0],a=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":a=[s[0],s[1]],n=[s[0],s[1]];break;case"L":i.push(...hr(a[0],a[1],s[0],s[1],t)),a=[s[0],s[1]];break;case"C":{const[c,l,h,u,f,d]=s;i.push(...NA(c,l,h,u,f,d,a,t)),a=[f,d];break}case"Z":i.push(...hr(a[0],a[1],n[0],n[1],t)),a=[n[0],n[1]]}return{type:"path",ops:i}}function wo(e,t){const r=[];for(const i of e)if(i.length){const n=t.maxRandomnessOffset||0,a=i.length;if(a>2){r.push({op:"move",data:[i[0][0]+st(n,t),i[0][1]+st(n,t)]});for(let o=1;o<a;o++)r.push({op:"lineTo",data:[i[o][0]+st(n,t),i[o][1]+st(n,t)]})}}return{type:"fillPath",ops:r}}function Xr(e,t){return function(r,i){let n=r.fillStyle||"hachure";if(!oe[n])switch(n){case"zigzag":oe[n]||(oe[n]=new BA(i));break;case"cross-hatch":oe[n]||(oe[n]=new EA(i));break;case"dots":oe[n]||(oe[n]=new FA(i));break;case"dashed":oe[n]||(oe[n]=new $A(i));break;case"zigzag-line":oe[n]||(oe[n]=new DA(i));break;default:n="hachure",oe[n]||(oe[n]=new Sc(i))}return oe[n]}(t,IA).fillPolygons(e,t)}function Eu(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function bm(e){return e.randomizer||(e.randomizer=new OA(e.seed||0)),e.randomizer.next()}function ss(e,t,r,i=1){return r.roughness*i*(bm(r)*(t-e)+e)}function st(e,t,r=1){return ss(-e,e,t,r)}function hr(e,t,r,i,n,a=!1){const o=a?n.disableMultiStrokeFill:n.disableMultiStroke,s=pl(e,t,r,i,n,!0,!1);if(o)return s;const c=pl(e,t,r,i,n,!0,!0);return s.concat(c)}function pl(e,t,r,i,n,a,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),c=Math.sqrt(s);let l=1;l=c<200?1:c>500?.4:-.0016668*c+1.233334;let h=n.maxRandomnessOffset||0;h*h*100>s&&(h=c/10);const u=h/2,f=.2+.2*bm(n);let d=n.bowing*n.maxRandomnessOffset*(i-t)/200,p=n.bowing*n.maxRandomnessOffset*(e-r)/200;d=st(d,n,l),p=st(p,n,l);const m=[],y=()=>st(u,n,l),x=()=>st(h,n,l),b=n.preserveVertices;return o?m.push({op:"move",data:[e+(b?0:y()),t+(b?0:y())]}):m.push({op:"move",data:[e+(b?0:st(h,n,l)),t+(b?0:st(h,n,l))]}),o?m.push({op:"bcurveTo",data:[d+e+(r-e)*f+y(),p+t+(i-t)*f+y(),d+e+2*(r-e)*f+y(),p+t+2*(i-t)*f+y(),r+(b?0:y()),i+(b?0:y())]}):m.push({op:"bcurveTo",data:[d+e+(r-e)*f+x(),p+t+(i-t)*f+x(),d+e+2*(r-e)*f+x(),p+t+2*(i-t)*f+x(),r+(b?0:x()),i+(b?0:x())]}),m}function ea(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+st(t,r),e[0][1]+st(t,r)]),i.push([e[0][0]+st(t,r),e[0][1]+st(t,r)]);for(let n=1;n<e.length;n++)i.push([e[n][0]+st(t,r),e[n][1]+st(t,r)]),n===e.length-1&&i.push([e[n][0]+st(t,r),e[n][1]+st(t,r)]);return os(i,null,r)}function os(e,t,r){const i=e.length,n=[];if(i>3){const a=[],o=1-r.curveTightness;n.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const c=e[s];a[0]=[c[0],c[1]],a[1]=[c[0]+(o*e[s+1][0]-o*e[s-1][0])/6,c[1]+(o*e[s+1][1]-o*e[s-1][1])/6],a[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],a[3]=[e[s+1][0],e[s+1][1]],n.push({op:"bcurveTo",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}}else i===3?(n.push({op:"move",data:[e[1][0],e[1][1]]}),n.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&n.push(...pl(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return n}function Fu(e,t,r,i,n,a,o,s){const c=[],l=[];if(s.roughness===0){e/=4,l.push([t+i*Math.cos(-e),r+n*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+n*Math.sin(h)];c.push(u),l.push(u)}l.push([t+i*Math.cos(0),r+n*Math.sin(0)]),l.push([t+i*Math.cos(e),r+n*Math.sin(e)])}else{const h=st(.5,s)-Math.PI/2;l.push([st(a,s)+t+.9*i*Math.cos(h-e),st(a,s)+r+.9*n*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let f=h;f<u;f+=e){const d=[st(a,s)+t+i*Math.cos(f),st(a,s)+r+n*Math.sin(f)];c.push(d),l.push(d)}l.push([st(a,s)+t+i*Math.cos(h+2*Math.PI+.5*o),st(a,s)+r+n*Math.sin(h+2*Math.PI+.5*o)]),l.push([st(a,s)+t+.98*i*Math.cos(h+o),st(a,s)+r+.98*n*Math.sin(h+o)]),l.push([st(a,s)+t+.9*i*Math.cos(h+.5*o),st(a,s)+r+.9*n*Math.sin(h+.5*o)])}return[l,c]}function $u(e,t,r,i,n,a,o,s,c){const l=a+st(.1,c),h=[];h.push([st(s,c)+t+.9*i*Math.cos(l-e),st(s,c)+r+.9*n*Math.sin(l-e)]);for(let u=l;u<=o;u+=e)h.push([st(s,c)+t+i*Math.cos(u),st(s,c)+r+n*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+n*Math.sin(o)]),h.push([t+i*Math.cos(o),r+n*Math.sin(o)]),os(h,null,c)}function NA(e,t,r,i,n,a,o,s){const c=[],l=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,f=s.preserveVertices;for(let d=0;d<u;d++)d===0?c.push({op:"move",data:[o[0],o[1]]}):c.push({op:"move",data:[o[0]+(f?0:st(l[0],s)),o[1]+(f?0:st(l[0],s))]}),h=f?[n,a]:[n+st(l[d],s),a+st(l[d],s)],c.push({op:"bcurveTo",data:[e+st(l[d],s),t+st(l[d],s),r+st(l[d],s),i+st(l[d],s),h[0],h[1]]});return c}function Ki(e){return[...e]}function Du(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(Ki(e[0]),Ki(e[1]),Ki(e[2]),Ki(e[2]));else{const n=[];n.push(e[0],e[0]);for(let s=1;s<e.length;s++)n.push(e[s]),s===e.length-1&&n.push(e[s]);const a=[],o=1-t;i.push(Ki(n[0]));for(let s=1;s+2<n.length;s++){const c=n[s];a[0]=[c[0],c[1]],a[1]=[c[0]+(o*n[s+1][0]-o*n[s-1][0])/6,c[1]+(o*n[s+1][1]-o*n[s-1][1])/6],a[2]=[n[s+1][0]+(o*n[s][0]-o*n[s+2][0])/6,n[s+1][1]+(o*n[s][1]-o*n[s+2][1])/6],a[3]=[n[s+1][0],n[s+1][1]],i.push(a[1],a[2],a[3])}}return i}function xa(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function zA(e,t,r){const i=xa(t,r);if(i===0)return xa(e,t);let n=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return n=Math.max(0,Math.min(1,n)),xa(e,br(t,r,n))}function br(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function gl(e,t,r,i){const n=i||[];if(function(s,c){const l=s[c+0],h=s[c+1],u=s[c+2],f=s[c+3];let d=3*h[0]-2*l[0]-f[0];d*=d;let p=3*h[1]-2*l[1]-f[1];p*=p;let m=3*u[0]-2*f[0]-l[0];m*=m;let y=3*u[1]-2*f[1]-l[1];return y*=y,d<m&&(d=m),p<y&&(p=y),d+p}(e,t)<r){const s=e[t+0];n.length?(a=n[n.length-1],o=s,Math.sqrt(xa(a,o))>1&&n.push(s)):n.push(s),n.push(e[t+3])}else{const c=e[t+0],l=e[t+1],h=e[t+2],u=e[t+3],f=br(c,l,.5),d=br(l,h,.5),p=br(h,u,.5),m=br(f,d,.5),y=br(d,p,.5),x=br(m,y,.5);gl([c,f,m,x],0,r,n),gl([x,y,p,u],0,r,n)}var a,o;return n}function WA(e,t){return ls(e,0,e.length,t)}function ls(e,t,r,i,n){const a=n||[],o=e[t],s=e[r-1];let c=0,l=1;for(let h=t+1;h<r-1;++h){const u=zA(e[h],o,s);u>c&&(c=u,l=h)}return Math.sqrt(c)>i?(ls(e,t,l+1,i,a),ls(e,l,r,i,a)):(a.length||a.push(o),a.push(s)),a}function ko(e,t=.15,r){const i=[],n=(e.length-1)/3;for(let a=0;a<n;a++)gl(e,3*a,t,i);return r&&r>0?ls(i,0,i.length,r):i}const ue="none";class cs{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,n,a){const o=this._o(a);return this._d("line",[ym(t,r,i,n,o)],o)}rectangle(t,r,i,n,a){const o=this._o(a),s=[],c=PA(t,r,i,n,o);if(o.fill){const l=[[t,r],[t+i,r],[t+i,r+n],[t,r+n]];o.fillStyle==="solid"?s.push(wo([l],o)):s.push(Xr([l],o))}return o.stroke!==ue&&s.push(c),this._d("rectangle",s,o)}ellipse(t,r,i,n,a){const o=this._o(a),s=[],c=xm(i,n,o),l=dl(t,r,o,c);if(o.fill)if(o.fillStyle==="solid"){const h=dl(t,r,o,c).opset;h.type="fillPath",s.push(h)}else s.push(Xr([l.estimatedPoints],o));return o.stroke!==ue&&s.push(l.opset),this._d("ellipse",s,o)}circle(t,r,i,n){const a=this.ellipse(t,r,i,i,n);return a.shape="circle",a}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[ya(t,!1,i)],i)}arc(t,r,i,n,a,o,s=!1,c){const l=this._o(c),h=[],u=Lu(t,r,i,n,a,o,s,!0,l);if(s&&l.fill)if(l.fillStyle==="solid"){const f=Object.assign({},l);f.disableMultiStroke=!0;const d=Lu(t,r,i,n,a,o,!0,!1,f);d.type="fillPath",h.push(d)}else h.push(function(f,d,p,m,y,x,b){const C=f,v=d;let k=Math.abs(p/2),_=Math.abs(m/2);k+=st(.01*k,b),_+=st(.01*_,b);let w=y,O=x;for(;w<0;)w+=2*Math.PI,O+=2*Math.PI;O-w>2*Math.PI&&(w=0,O=2*Math.PI);const N=(O-w)/b.curveStepCount,$=[];for(let S=w;S<=O;S+=N)$.push([C+k*Math.cos(S),v+_*Math.sin(S)]);return $.push([C+k*Math.cos(O),v+_*Math.sin(O)]),$.push([C,v]),Xr([$],b)}(t,r,i,n,a,o,l));return l.stroke!==ue&&h.push(u),this._d("arc",h,l)}curve(t,r){const i=this._o(r),n=[],a=Au(t,i);if(i.fill&&i.fill!==ue)if(i.fillStyle==="solid"){const o=Au(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));n.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const c=typeof s[0][0]=="number"?[s]:s;for(const l of c)l.length<3?o.push(...l):l.length===3?o.push(...ko(Du([l[0],l[0],l[1],l[2]]),10,(1+i.roughness)/2)):o.push(...ko(Du(l),10,(1+i.roughness)/2))}o.length&&n.push(Xr([o],i))}return i.stroke!==ue&&n.push(a),this._d("curve",n,i)}polygon(t,r){const i=this._o(r),n=[],a=ya(t,!0,i);return i.fill&&(i.fillStyle==="solid"?n.push(wo([t],i)):n.push(Xr([t],i))),i.stroke!==ue&&n.push(a),this._d("polygon",n,i)}path(t,r){const i=this._o(r),n=[];if(!t)return this._d("path",n,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const a=i.fill&&i.fill!=="transparent"&&i.fill!==ue,o=i.stroke!==ue,s=!!(i.simplification&&i.simplification<1),c=function(h,u,f){const d=gm(pm(Tc(h))),p=[];let m=[],y=[0,0],x=[];const b=()=>{x.length>=4&&m.push(...ko(x,u)),x=[]},C=()=>{b(),m.length&&(p.push(m),m=[])};for(const{key:k,data:_}of d)switch(k){case"M":C(),y=[_[0],_[1]],m.push(y);break;case"L":b(),m.push([_[0],_[1]]);break;case"C":if(!x.length){const w=m.length?m[m.length-1]:y;x.push([w[0],w[1]])}x.push([_[0],_[1]]),x.push([_[2],_[3]]),x.push([_[4],_[5]]);break;case"Z":b(),m.push([y[0],y[1]])}if(C(),!f)return p;const v=[];for(const k of p){const _=WA(k,f);_.length&&v.push(_)}return v}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),l=Bu(t,i);if(a)if(i.fillStyle==="solid")if(c.length===1){const h=Bu(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));n.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else n.push(wo(c,i));else n.push(Xr(c,i));return o&&(s?c.forEach(h=>{n.push(ya(h,!1,i))}):n.push(l)),this._d("path",n,i)}opsToPath(t,r){let i="";for(const n of t.ops){const a=typeof r=="number"&&r>=0?n.data.map(o=>+o.toFixed(r)):n.data;switch(n.op){case"move":i+=`M${a[0]} ${a[1]} `;break;case"bcurveTo":i+=`C${a[0]} ${a[1]}, ${a[2]} ${a[3]}, ${a[4]} ${a[5]} `;break;case"lineTo":i+=`L${a[0]} ${a[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,n=[];for(const a of r){let o=null;switch(a.type){case"path":o={d:this.opsToPath(a),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:ue};break;case"fillPath":o={d:this.opsToPath(a),stroke:ue,strokeWidth:0,fill:i.fill||ue};break;case"fillSketch":o=this.fillSketch(a,i)}o&&n.push(o)}return n}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||ue,strokeWidth:i,fill:ue}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class qA{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new cs(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),n=this.ctx,a=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":n.save(),n.strokeStyle=i.stroke==="none"?"transparent":i.stroke,n.lineWidth=i.strokeWidth,i.strokeLineDash&&n.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(n.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(n,o,a),n.restore();break;case"fillPath":{n.save(),n.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(n,o,a,s),n.restore();break}case"fillSketch":this.fillSketch(n,o,i)}}fillSketch(t,r,i){let n=i.fillWeight;n<0&&(n=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=n,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,n="nonzero"){t.beginPath();for(const a of r.ops){const o=typeof i=="number"&&i>=0?a.data.map(s=>+s.toFixed(i)):a.data;switch(a.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,n,a){const o=this.gen.line(t,r,i,n,a);return this.draw(o),o}rectangle(t,r,i,n,a){const o=this.gen.rectangle(t,r,i,n,a);return this.draw(o),o}ellipse(t,r,i,n,a){const o=this.gen.ellipse(t,r,i,n,a);return this.draw(o),o}circle(t,r,i,n){const a=this.gen.circle(t,r,i,n);return this.draw(a),a}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,n,a,o,s=!1,c){const l=this.gen.arc(t,r,i,n,a,o,s,c);return this.draw(l),l}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const ra="http://www.w3.org/2000/svg";class HA{constructor(t,r){this.svg=t,this.gen=new cs(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,a=n.createElementNS(ra,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let c=null;switch(s.type){case"path":c=n.createElementNS(ra,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke",i.stroke),c.setAttribute("stroke-width",i.strokeWidth+""),c.setAttribute("fill","none"),i.strokeLineDash&&c.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&c.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":c=n.createElementNS(ra,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke","none"),c.setAttribute("stroke-width","0"),c.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||c.setAttribute("fill-rule","evenodd");break;case"fillSketch":c=this.fillSketch(n,s,i)}c&&a.appendChild(c)}return a}fillSketch(t,r,i){let n=i.fillWeight;n<0&&(n=i.strokeWidth/2);const a=t.createElementNS(ra,"path");return a.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),a.setAttribute("stroke",i.fill||""),a.setAttribute("stroke-width",n+""),a.setAttribute("fill","none"),i.fillLineDash&&a.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&a.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),a}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,n,a){const o=this.gen.line(t,r,i,n,a);return this.draw(o)}rectangle(t,r,i,n,a){const o=this.gen.rectangle(t,r,i,n,a);return this.draw(o)}ellipse(t,r,i,n,a){const o=this.gen.ellipse(t,r,i,n,a);return this.draw(o)}circle(t,r,i,n){const a=this.gen.circle(t,r,i,n);return this.draw(a)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,n,a,o,s=!1,c){const l=this.gen.arc(t,r,i,n,a,o,s,c);return this.draw(l)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var V={canvas:(e,t)=>new qA(e,t),svg:(e,t)=>new HA(e,t),generator:e=>new cs(e),newSeed:()=>cs.newSeed()},ht=g(async(e,t,r)=>{var u,f;let i;const n=t.useHtmlLabels||$t((u=yt())==null?void 0:u.htmlLabels);r?i=r:i="node default";const a=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=a.insert("g").attr("class","label").attr("style",re(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const c=await dr(o,Ar(qr(s),yt()),{useHtmlLabels:n,width:t.width||((f=yt().flowchart)==null?void 0:f.wrappingWidth),cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let l=c.getBBox();const h=((t==null?void 0:t.padding)??0)/2;if(n){const d=c.children[0],p=pt(c),m=d.getElementsByTagName("img");if(m){const y=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(x=>new Promise(b=>{function C(){if(x.style.display="flex",x.style.flexDirection="column",y){const v=yt().fontSize?yt().fontSize:window.getComputedStyle(document.body).fontSize,k=5,[_=ff.fontSize]=$s(v),w=_*k+"px";x.style.minWidth=w,x.style.maxWidth=w}else x.style.width="100%";b(x)}g(C,"setupImage"),setTimeout(()=>{x.complete&&C()}),x.addEventListener("error",C),x.addEventListener("load",C)})))}l=d.getBoundingClientRect(),p.attr("width",l.width),p.attr("height",l.height)}return n?o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"):o.attr("transform","translate(0, "+-l.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:a,bbox:l,halfPadding:h,label:o}},"labelHelper"),vo=g(async(e,t,r)=>{var c,l,h,u,f,d;const i=r.useHtmlLabels||$t((l=(c=yt())==null?void 0:c.flowchart)==null?void 0:l.htmlLabels),n=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),a=await dr(n,Ar(qr(t),yt()),{useHtmlLabels:i,width:r.width||((u=(h=yt())==null?void 0:h.flowchart)==null?void 0:u.wrappingWidth),style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=a.getBBox();const s=r.padding/2;if($t((d=(f=yt())==null?void 0:f.flowchart)==null?void 0:d.htmlLabels)){const p=a.children[0],m=pt(a);o=p.getBoundingClientRect(),m.attr("width",o.width),m.attr("height",o.height)}return i?n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):n.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),n.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:n}},"insertLabel"),tt=g((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),ot=g((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function gt(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}g(gt,"createPathFromPoints");function ur(e,t,r,i,n,a){const o=[],c=r-e,l=i-t,h=c/a,u=2*Math.PI/h,f=t+l/2;for(let d=0;d<=50;d++){const p=d/50,m=e+p*c,y=f+n*Math.sin(u*(m-e));o.push({x:m,y})}return o}g(ur,"generateFullSineWavePoints");function Mc(e,t,r,i,n,a){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),p=t+r*Math.sin(f);o.push({x:-d,y:-p})}return o}g(Mc,"generateCirclePoints");var UA=g((e,t)=>{var r=e.x,i=e.y,n=t.x-r,a=t.y-i,o=e.width/2,s=e.height/2,c,l;return Math.abs(a)*o>Math.abs(n)*s?(a<0&&(s=-s),c=a===0?0:s*n/a,l=s):(n<0&&(o=-o),c=o,l=n===0?0:o*a/n),{x:r+c,y:i+l}},"intersectRect"),Ei=UA;function _m(e,t){t&&e.attr("style",t)}g(_m,"applyStyle");async function Cm(e){const t=pt(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div");let i=e.label;e.label&&xi(e.label)&&(i=await Al(e.label.replace(Ai.lineBreakRegex,`
`),yt()));const n=e.isNode?"nodeLabel":"edgeLabel";return r.html('<span class="'+n+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+i+"</span>"),_m(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}g(Cm,"addHtmlLabel");var YA=g(async(e,t,r,i)=>{let n=e||"";if(typeof n=="object"&&(n=n[0]),$t(yt().flowchart.htmlLabels)){n=n.replace(/\\n|\n/g,"<br />"),R.info("vertexText"+n);const a={isNode:i,label:qr(n).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await Cm(a)}else{const a=document.createElementNS("http://www.w3.org/2000/svg","text");a.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof n=="string"?o=n.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(n)?o=n:o=[];for(const s of o){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),r?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=s.trim(),a.appendChild(c)}return a}},"createLabel"),Sr=YA,ir=g((e,t,r,i,n)=>["M",e+n,t,"H",e+r-n,"A",n,n,0,0,1,e+r,t+n,"V",t+i-n,"A",n,n,0,0,1,e+r-n,t+i,"H",e+n,"A",n,n,0,0,1,e,t+i-n,"V",t+n,"A",n,n,0,0,1,e+n,t,"Z"].join(" "),"createRoundedRectPathD"),jA=g(e=>{const{handDrawnSeed:t}=yt();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),Fi=g(e=>{const t=GA([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),GA=g(e=>{const t=new Map;return e.forEach(r=>{const[i,n]=r.split(":");t.set(i.trim(),n==null?void 0:n.trim())}),t},"styles2Map"),wm=g(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),et=g(e=>{const{stylesArray:t}=Fi(e),r=[],i=[],n=[],a=[];return t.forEach(o=>{const s=o[0];wm(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&n.push(o.join(":")+" !important"),s==="fill"&&a.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:n,backgroundStyles:a}},"styles2String"),K=g((e,t)=>{var c;const{themeVariables:r,handDrawnSeed:i}=yt(),{nodeBorder:n,mainBkg:a}=r,{stylesMap:o}=Fi(e);return Object.assign({roughness:.7,fill:o.get("fill")||a,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||n,seed:i,strokeWidth:((c=o.get("stroke-width"))==null?void 0:c.replace("px",""))||1.3,fillLineDash:[0,0]},t)},"userNodeOverrides"),km=g(async(e,t)=>{R.info("Creating subgraph rect for ",t.id,t);const r=yt(),{themeVariables:i,handDrawnSeed:n}=r,{clusterBkg:a,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=et(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=$t(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),p=await dr(d,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0});let m=p.getBBox();if($t(r.flowchart.htmlLabels)){const w=p.children[0],O=pt(p);m=w.getBoundingClientRect(),O.attr("width",m.width),O.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;R.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const w=V.svg(u),O=K(t,{roughness:.7,fill:a,stroke:o,fillWeight:3,seed:n}),N=w.path(ir(b,C,y,x,0),O);v=u.insert(()=>(R.debug("Rough node insert CXC",N),N),":first-child"),v.select("path:nth-child(2)").attr("style",l.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:k}=Wl(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+k})`),s){const w=d.select("span");w&&w.attr("style",s)}const _=v.node().getBBox();return t.offsetX=0,t.width=_.width,t.height=_.height,t.offsetY=m.height-t.padding/2,t.intersect=function(w){return Ei(t,w)},{cluster:u,labelBBox:m}},"rect"),VA=g((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),n=0*t.padding,a=n/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-a).attr("y",t.y-t.height/2-a).attr("width",t.width+n).attr("height",t.height+n).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return Ei(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),XA=g(async(e,t)=>{const r=yt(),{themeVariables:i,handDrawnSeed:n}=r,{altBackground:a,compositeBackground:o,compositeTitleBackground:s,nodeBorder:c}=i,l=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=l.insert("g",":first-child"),u=l.insert("g").attr("class","cluster-label");let f=l.append("rect");const d=u.node().appendChild(await Sr(t.label,t.labelStyle,void 0,!0));let p=d.getBBox();if($t(r.flowchart.htmlLabels)){const N=d.children[0],$=pt(d);p=N.getBoundingClientRect(),$.attr("width",p.width),$.attr("height",p.height)}const m=0*t.padding,y=m/2,x=(t.width<=p.width+t.padding?p.width+t.padding:t.width)+m;t.width<=p.width+t.padding?t.diff=(x-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height+m,C=t.height+m-p.height-6,v=t.x-x/2,k=t.y-b/2;t.width=x;const _=t.y-t.height/2-y+p.height+2;let w;if(t.look==="handDrawn"){const N=t.cssClasses.includes("statediagram-cluster-alt"),$=V.svg(l),S=t.rx||t.ry?$.path(ir(v,k,x,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:c,seed:n}):$.rectangle(v,k,x,b,{seed:n});w=l.insert(()=>S,":first-child");const I=$.rectangle(v,_,x,C,{fill:N?a:o,fillStyle:N?"hachure":"solid",stroke:c,seed:n});w=l.insert(()=>S,":first-child"),f=l.insert(()=>I)}else w=h.insert("rect",":first-child"),w.attr("class","outer").attr("x",v).attr("y",k).attr("width",x).attr("height",b).attr("data-look",t.look),f.attr("class","inner").attr("x",v).attr("y",_).attr("width",x).attr("height",C);u.attr("transform",`translate(${t.x-p.width/2}, ${k+1-($t(r.flowchart.htmlLabels)?0:3)})`);const O=w.node().getBBox();return t.height=O.height,t.offsetX=0,t.offsetY=p.height-t.padding/2,t.labelBBox=p,t.intersect=function(N){return Ei(t,N)},{cluster:l,labelBBox:p}},"roundedWithTitle"),ZA=g(async(e,t)=>{R.info("Creating subgraph rect for ",t.id,t);const r=yt(),{themeVariables:i,handDrawnSeed:n}=r,{clusterBkg:a,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=et(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=$t(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),p=await dr(d,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0,width:t.width});let m=p.getBBox();if($t(r.flowchart.htmlLabels)){const w=p.children[0],O=pt(p);m=w.getBoundingClientRect(),O.attr("width",m.width),O.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;R.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const w=V.svg(u),O=K(t,{roughness:.7,fill:a,stroke:o,fillWeight:4,seed:n}),N=w.path(ir(b,C,y,x,t.rx),O);v=u.insert(()=>(R.debug("Rough node insert CXC",N),N),":first-child"),v.select("path:nth-child(2)").attr("style",l.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:k}=Wl(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+k})`),s){const w=d.select("span");w&&w.attr("style",s)}const _=v.node().getBBox();return t.offsetX=0,t.width=_.width,t.height=_.height,t.offsetY=m.height-t.padding/2,t.intersect=function(w){return Ei(t,w)},{cluster:u,labelBBox:m}},"kanbanSection"),KA=g((e,t)=>{const r=yt(),{themeVariables:i,handDrawnSeed:n}=r,{nodeBorder:a}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),c=0*t.padding,l=t.width+c;t.diff=-t.padding;const h=t.height+c,u=t.x-l/2,f=t.y-h/2;t.width=l;let d;if(t.look==="handDrawn"){const y=V.svg(o).rectangle(u,f,l,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:a,seed:n});d=o.insert(()=>y,":first-child")}else d=s.insert("rect",":first-child"),d.attr("class","divider").attr("x",u).attr("y",f).attr("width",l).attr("height",h).attr("data-look",t.look);const p=d.node().getBBox();return t.height=p.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return Ei(t,m)},{cluster:o,labelBBox:{}}},"divider"),QA=km,JA={rect:km,squareRect:QA,roundedWithTitle:XA,noteGroup:VA,divider:KA,kanbanSection:ZA},vm=new Map,tL=g(async(e,t)=>{const r=t.shape||"rect",i=await JA[r](e,t);return vm.set(t.id,i),i},"insertCluster"),b3=g(()=>{vm=new Map},"clear");function Sm(e,t){return e.intersect(t)}g(Sm,"intersectNode");var eL=Sm;function Tm(e,t,r,i){var n=e.x,a=e.y,o=n-i.x,s=a-i.y,c=Math.sqrt(t*t*s*s+r*r*o*o),l=Math.abs(t*r*o/c);i.x<n&&(l=-l);var h=Math.abs(t*r*s/c);return i.y<a&&(h=-h),{x:n+l,y:a+h}}g(Tm,"intersectEllipse");var Mm=Tm;function Am(e,t,r){return Mm(e,t,t,r)}g(Am,"intersectCircle");var rL=Am;function Lm(e,t,r,i){var n,a,o,s,c,l,h,u,f,d,p,m,y,x,b;if(n=t.y-e.y,o=e.x-t.x,c=t.x*e.y-e.x*t.y,f=n*r.x+o*r.y+c,d=n*i.x+o*i.y+c,!(f!==0&&d!==0&&ml(f,d))&&(a=i.y-r.y,s=r.x-i.x,l=i.x*r.y-r.x*i.y,h=a*e.x+s*e.y+l,u=a*t.x+s*t.y+l,!(h!==0&&u!==0&&ml(h,u))&&(p=n*s-a*o,p!==0)))return m=Math.abs(p/2),y=o*l-s*c,x=y<0?(y-m)/p:(y+m)/p,y=a*c-n*l,b=y<0?(y-m)/p:(y+m)/p,{x,y:b}}g(Lm,"intersectLine");function ml(e,t){return e*t>0}g(ml,"sameSign");var iL=Lm;function Bm(e,t,r){let i=e.x,n=e.y,a=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let c=i-e.width/2-o,l=n-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],f=t[h<t.length-1?h+1:0],d=iL(e,r,{x:c+u.x,y:l+u.y},{x:c+f.x,y:l+f.y});d&&a.push(d)}return a.length?(a.length>1&&a.sort(function(h,u){let f=h.x-r.x,d=h.y-r.y,p=Math.sqrt(f*f+d*d),m=u.x-r.x,y=u.y-r.y,x=Math.sqrt(m*m+y*y);return p<x?-1:p===x?0:1}),a[0]):e}g(Bm,"intersectPolygon");var nL=Bm,j={node:eL,circle:rL,ellipse:Mm,polygon:nL,rect:Ei};function Em(e,t){const{labelStyles:r}=et(t);t.labelStyle=r;const i=ot(t);let n=i;i||(n="anchor");const a=e.insert("g").attr("class",n).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,c=V.svg(a),l=K(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(l.roughness=0);const h=c.circle(0,0,o*2,l),u=a.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",re(s)),tt(t,u),t.intersect=function(f){return R.info("Circle intersect",t,o,f),j.circle(t,o,f)},a}g(Em,"anchor");function yl(e,t,r,i,n,a,o){const c=(e+r)/2,l=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,f=(i-t)/2,d=u/n,p=f/a,m=Math.sqrt(d**2+p**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=c+y*a*Math.sin(h)*(o?-1:1),b=l-y*n*Math.cos(h)*(o?-1:1),C=Math.atan2((t-b)/a,(e-x)/n);let k=Math.atan2((i-b)/a,(r-x)/n)-C;o&&k<0&&(k+=2*Math.PI),!o&&k>0&&(k-=2*Math.PI);const _=[];for(let w=0;w<20;w++){const O=w/19,N=C+O*k,$=x+n*Math.cos(N),S=b+a*Math.sin(N);_.push({x:$,y:S})}return _}g(yl,"generateArcPoints");async function Fm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=a.width+t.padding+20,s=a.height+t.padding,c=s/2,l=c/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...yl(-o/2,-s/2,-o/2,s/2,l,c,!1),{x:o/2,y:s/2},...yl(o/2,s/2,o/2,-s/2,l,c,!0)],f=V.svg(n),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=gt(u),m=f.path(p,d),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${l/2}, 0)`),tt(t,y),t.intersect=function(x){return j.polygon(t,u,x)},n}g(Fm,"bowTieRect");function nr(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(n){return n.x+","+n.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}g(nr,"insertPolygonShape");async function $m(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=a.height+t.padding,s=12,c=a.width+t.padding+s,l=0,h=c,u=-o,f=0,d=[{x:l+s,y:u},{x:h,y:u},{x:h,y:f},{x:l,y:f},{x:l,y:u+s},{x:l+s,y:u}];let p;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=V.svg(n),x=K(t,{}),b=gt(d),C=y.path(b,x);p=n.insert(()=>C,":first-child").attr("transform",`translate(${-c/2}, ${o/2})`),m&&p.attr("style",m)}else p=nr(n,c,o,d);return i&&p.attr("style",i),tt(t,p),t.intersect=function(y){return j.polygon(t,d,y)},n}g($m,"card");function Dm(e,t){const{nodeStyles:r}=et(t);t.label="";const i=e.insert("g").attr("class",ot(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,a=Math.max(28,t.width??0),o=[{x:0,y:a/2},{x:a/2,y:0},{x:0,y:-a/2},{x:-a/2,y:0}],s=V.svg(i),c=K(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=gt(o),h=s.path(l,c),u=i.insert(()=>h,":first-child");return n&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(f){return j.polygon(t,o,f)},i}g(Dm,"choice");async function Om(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,halfPadding:o}=await ht(e,t,ot(t)),s=a.width/2+o;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=V.svg(n),u=K(t,{}),f=h.circle(0,0,s*2,u);c=n.insert(()=>f,":first-child"),c.attr("class","basic label-container").attr("style",re(l))}else c=n.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return tt(t,c),t.intersect=function(h){return R.info("Circle intersect",t,s,h),j.circle(t,s,h)},n}g(Om,"circle");function Rm(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,n={x:i/2*t,y:i/2*r},a={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${a.x},${a.y} L ${s.x},${s.y}
                   M ${n.x},${n.y} L ${o.x},${o.y}`}g(Rm,"createLine");function Im(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r,t.label="";const n=e.insert("g").attr("class",ot(t)).attr("id",t.domId??t.id),a=Math.max(30,(t==null?void 0:t.width)??0),{cssStyles:o}=t,s=V.svg(n),c=K(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=s.circle(0,0,a*2,c),h=Rm(a),u=s.path(h,c),f=n.insert(()=>l,":first-child");return f.insert(()=>u),o&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",i),tt(t,f),t.intersect=function(d){return R.info("crossedCircle intersect",t,{radius:a,point:d}),j.circle(t,a,d)},n}g(Im,"crossedCircle");function Ye(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),p=t+r*Math.sin(f);o.push({x:-d,y:-p})}return o}g(Ye,"generateCirclePoints");async function Pm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ye(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Ye(s/2+l*2,-l,l,20,-180,-270),...Ye(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Ye(s/2,c/2,l,20,0,90)],f=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Ye(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Ye(s/2+s*.1,-l,l,20,-180,-270),...Ye(s/2+s*.1,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Ye(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2,y:c/2+l}],d=V.svg(n),p=K(t,{fill:"none"});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const y=gt(u).replace("Z",""),x=d.path(y,p),b=gt(f),C=d.path(b,{...p}),v=n.insert("g",":first-child");return v.insert(()=>C,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${l}, 0)`),o.attr("transform",`translate(${-s/2+l-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),tt(t,v),t.intersect=function(k){return j.polygon(t,f,k)},n}g(Pm,"curlyBraceLeft");function je(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),p=t+r*Math.sin(f);o.push({x:d,y:p})}return o}g(je,"generateCirclePoints");async function Nm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...je(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...je(s/2+l*2,-l,l,20,-180,-270),...je(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...je(s/2,c/2,l,20,0,90)],f=[{x:-s/2,y:-c/2-l},{x:s/2,y:-c/2-l},...je(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...je(s/2+l*2,-l,l,20,-180,-270),...je(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...je(s/2,c/2,l,20,0,90),{x:s/2,y:c/2+l},{x:-s/2,y:c/2+l}],d=V.svg(n),p=K(t,{fill:"none"});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const y=gt(u).replace("Z",""),x=d.path(y,p),b=gt(f),C=d.path(b,{...p}),v=n.insert("g",":first-child");return v.insert(()=>C,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${-l}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),tt(t,v),t.intersect=function(k){return j.polygon(t,f,k)},n}g(Nm,"curlyBraceRight");function Wt(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),p=t+r*Math.sin(f);o.push({x:-d,y:-p})}return o}g(Wt,"generateCirclePoints");async function zm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Wt(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Wt(s/2+l*2,-l,l,20,-180,-270),...Wt(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Wt(s/2,c/2,l,20,0,90)],f=[...Wt(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Wt(-s/2-l/2,-l,l,20,0,90),...Wt(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Wt(-s/2+l+l/2,c/2,l,30,-180,-270)],d=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Wt(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Wt(s/2+l*2,-l,l,20,-180,-270),...Wt(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Wt(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2-l-l/2,y:c/2+l},...Wt(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Wt(-s/2-l/2,-l,l,20,0,90),...Wt(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Wt(-s/2+l+l/2,c/2,l,30,-180,-270)],p=V.svg(n),m=K(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const x=gt(u).replace("Z",""),b=p.path(x,m),v=gt(f).replace("Z",""),k=p.path(v,m),_=gt(d),w=p.path(_,{...m}),O=n.insert("g",":first-child");return O.insert(()=>w,":first-child").attr("stroke-opacity",0),O.insert(()=>b,":first-child"),O.insert(()=>k,":first-child"),O.attr("class","text"),h&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",i),O.attr("transform",`translate(${l-l/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),tt(t,O),t.intersect=function(N){return j.polygon(t,d,N)},n}g(zm,"curlyBraces");async function Wm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=80,s=20,c=Math.max(o,(a.width+(t.padding??0)*2)*1.25,(t==null?void 0:t.width)??0),l=Math.max(s,a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,f=V.svg(n),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=c,m=l,y=p-h,x=m/4,b=[{x:y,y:0},{x,y:0},{x:0,y:m/2},{x,y:m},{x:y,y:m},...Mc(-y,-m/2,h,50,270,90)],C=gt(b),v=f.path(C,d),k=n.insert(()=>v,":first-child");return k.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&k.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&k.selectChildren("path").attr("style",i),k.attr("transform",`translate(${-c/2}, ${-l/2})`),tt(t,k),t.intersect=function(_){return j.polygon(t,b,_)},n}g(Wm,"curvedTrapezoid");var aL=g((e,t,r,i,n,a)=>[`M${e},${t+a}`,`a${n},${a} 0,0,0 ${r},0`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),sL=g((e,t,r,i,n,a)=>[`M${e},${t+a}`,`M${e+r},${t+a}`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),oL=g((e,t,r,i,n,a)=>[`M${e-r/2},${-i/2}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function qm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+t.padding,t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(a.height+l+t.padding,t.height??0);let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const d=V.svg(n),p=sL(0,0,s,h,c,l),m=oL(0,l,s,h,c,l),y=d.path(p,K(t,{})),x=d.path(m,K(t,{fill:"none"}));u=n.insert(()=>x,":first-child"),u=n.insert(()=>y,":first-child"),u.attr("class","basic label-container"),f&&u.attr("style",f)}else{const d=aL(0,0,s,h,c,l);u=n.insert("path",":first-child").attr("d",d).attr("class","basic label-container").attr("style",re(f)).attr("style",i)}return u.attr("label-offset-y",l),u.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),tt(t,u),o.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+(t.padding??0)/1.5-(a.y-(a.top??0))})`),t.intersect=function(d){const p=j.rect(t,d),m=p.x-(t.x??0);if(c!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(p.y-(t.y??0))>(t.height??0)/2-l)){let y=l*l*(1-m*m/(c*c));y>0&&(y=Math.sqrt(y)),y=l-y,d.y-(t.y??0)>0&&(y=-y),p.y+=y}return p},n}g(qm,"cylinder");async function Hm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=a.width+t.padding,c=a.height+t.padding,l=c*.2,h=-s/2,u=-c/2-l/2,{cssStyles:f}=t,d=V.svg(n),p=K(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const m=[{x:h,y:u+l},{x:-h,y:u+l},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),p),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(a.x-(a.left??0))}, ${u+l+(t.padding??0)/2-(a.y-(a.top??0))})`),tt(t,x),t.intersect=function(b){return j.rect(t,b)},n}g(Hm,"dividedRectangle");async function Um(e,t){var f,d;const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,halfPadding:o}=await ht(e,t,ot(t)),c=a.width/2+o+5,l=a.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const p=V.svg(n),m=K(t,{roughness:.2,strokeWidth:2.5}),y=K(t,{roughness:.2,strokeWidth:1.5}),x=p.circle(0,0,c*2,m),b=p.circle(0,0,l*2,y);h=n.insert("g",":first-child"),h.attr("class",re(t.cssClasses)).attr("style",re(u)),(f=h.node())==null||f.appendChild(x),(d=h.node())==null||d.appendChild(b)}else{h=n.insert("g",":first-child");const p=h.insert("circle",":first-child"),m=h.insert("circle");h.attr("class","basic label-container").attr("style",i),p.attr("class","outer-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0)}return tt(t,h),t.intersect=function(p){return R.info("DoubleCircle intersect",t,c,p),j.circle(t,c,p)},n}g(Um,"doublecircle");function Ym(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:n}=et(t);t.label="",t.labelStyle=i;const a=e.insert("g").attr("class",ot(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,c=V.svg(a),{nodeBorder:l}=r,h=K(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=c.circle(0,0,o*2,h),f=a.insert(()=>u,":first-child");return f.selectAll("path").attr("style",`fill: ${l} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",n),tt(t,f),t.intersect=function(d){return R.info("filledCircle intersect",t,{radius:o,point:d}),j.circle(t,o,d)},a}g(Ym,"filledCircle");async function jm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=a.width+(t.padding??0),c=s+a.height,l=s+a.height,h=[{x:0,y:-c},{x:l,y:-c},{x:l/2,y:0}],{cssStyles:u}=t,f=V.svg(n),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=gt(h),m=f.path(p,d),y=n.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=c,tt(t,y),o.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${-c/2+(t.padding??0)/2+(a.y-(a.top??0))})`),t.intersect=function(x){return R.info("Triangle intersect",t,h,x),j.polygon(t,h,x)},n}g(jm,"flippedTriangle");function Gm(e,t,{dir:r,config:{state:i,themeVariables:n}}){const{nodeStyles:a}=et(t);t.label="";const o=e.insert("g").attr("class",ot(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let c=Math.max(70,(t==null?void 0:t.width)??0),l=Math.max(10,(t==null?void 0:t.height)??0);r==="LR"&&(c=Math.max(10,(t==null?void 0:t.width)??0),l=Math.max(70,(t==null?void 0:t.height)??0));const h=-1*c/2,u=-1*l/2,f=V.svg(o),d=K(t,{stroke:n.lineColor,fill:n.lineColor});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=f.rectangle(h,u,c,l,d),m=o.insert(()=>p,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",a),tt(t,m);const y=(i==null?void 0:i.padding)??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(x){return j.rect(t,x)},o}g(Gm,"forkJoin");async function Vm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const n=80,a=50,{shapeSvg:o,bbox:s}=await ht(e,t,ot(t)),c=Math.max(n,s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a,s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,f=V.svg(o),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=[{x:-c/2,y:-l/2},{x:c/2-h,y:-l/2},...Mc(-c/2+h,0,h,50,90,270),{x:c/2-h,y:l/2},{x:-c/2,y:l/2}],m=gt(p),y=f.path(m,d),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),tt(t,x),t.intersect=function(b){return R.info("Pill intersect",t,{radius:h,point:b}),j.polygon(t,p,b)},o}g(Vm,"halfRoundedRectangle");var lL=g((e,t,r,i,n)=>[`M${e+n},${t}`,`L${e+r-n},${t}`,`L${e+r},${t-i/2}`,`L${e+r-n},${t-i}`,`L${e+n},${t-i}`,`L${e},${t-i/2}`,"Z"].join(" "),"createHexagonPathD");async function Xm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=4,s=a.height+t.padding,c=s/o,l=a.width+2*c+t.padding,h=[{x:c,y:0},{x:l-c,y:0},{x:l,y:-s/2},{x:l-c,y:-s},{x:c,y:-s},{x:0,y:-s/2}];let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const d=V.svg(n),p=K(t,{}),m=lL(0,0,l,s,c),y=d.path(m,p);u=n.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${s/2})`),f&&u.attr("style",f)}else u=nr(n,l,s,h);return i&&u.attr("style",i),t.width=l,t.height=s,tt(t,u),t.intersect=function(d){return j.polygon(t,h,d)},n}g(Xm,"hexagon");async function Zm(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.label="",t.labelStyle=r;const{shapeSvg:n}=await ht(e,t,ot(t)),a=Math.max(30,(t==null?void 0:t.width)??0),o=Math.max(30,(t==null?void 0:t.height)??0),{cssStyles:s}=t,c=V.svg(n),l=K(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const h=[{x:0,y:0},{x:a,y:0},{x:0,y:o},{x:a,y:o}],u=gt(h),f=c.path(u,l),d=n.insert(()=>f,":first-child");return d.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",i),d.attr("transform",`translate(${-a/2}, ${-o/2})`),tt(t,d),t.intersect=function(p){return R.info("Pill intersect",t,{points:h}),j.polygon(t,h,p)},n}g(Zm,"hourglass");async function Km(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=et(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await ht(e,t,"icon-shape default"),f=t.pos==="t",d=s,p=s,{nodeBorder:m}=r,{stylesMap:y}=Fi(t),x=-p/2,b=-d/2,C=t.label?8:0,v=V.svg(l),k=K(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const _=v.rectangle(x,b,p,d,k),w=Math.max(p,h.width),O=d+h.height+C,N=v.rectangle(-w/2,-O/2,w,O,{...k,fill:"transparent",stroke:"none"}),$=l.insert(()=>_,":first-child"),S=l.insert(()=>N);if(t.icon){const I=l.append("g");I.html(`<g>${await xs(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const E=I.node().getBBox(),A=E.width,F=E.height,L=E.x,D=E.y;I.attr("transform",`translate(${-A/2-L},${f?h.height/2+C/2-F/2-D:-h.height/2-C/2-F/2-D})`),I.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${f?-O/2:O/2-h.height})`),$.attr("transform",`translate(0,${f?h.height/2+C/2:-h.height/2-C/2})`),tt(t,S),t.intersect=function(I){if(R.info("iconSquare intersect",t,I),!t.label)return j.rect(t,I);const E=t.x??0,A=t.y??0,F=t.height??0;let L=[];return f?L=[{x:E-h.width/2,y:A-F/2},{x:E+h.width/2,y:A-F/2},{x:E+h.width/2,y:A-F/2+h.height+C},{x:E+p/2,y:A-F/2+h.height+C},{x:E+p/2,y:A+F/2},{x:E-p/2,y:A+F/2},{x:E-p/2,y:A-F/2+h.height+C},{x:E-h.width/2,y:A-F/2+h.height+C}]:L=[{x:E-p/2,y:A-F/2},{x:E+p/2,y:A-F/2},{x:E+p/2,y:A-F/2+d},{x:E+h.width/2,y:A-F/2+d},{x:E+h.width/2/2,y:A+F/2},{x:E-h.width/2,y:A+F/2},{x:E-h.width/2,y:A-F/2+d},{x:E-p/2,y:A-F/2+d}],j.polygon(t,L,I)},l}g(Km,"icon");async function Qm(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=et(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await ht(e,t,"icon-shape default"),f=20,d=t.label?8:0,p=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:x}=Fi(t),b=V.svg(l),C=K(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=x.get("fill");C.stroke=v??y;const k=l.append("g");t.icon&&k.html(`<g>${await xs(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=k.node().getBBox(),w=_.width,O=_.height,N=_.x,$=_.y,S=Math.max(w,O)*Math.SQRT2+f*2,I=b.circle(0,0,S,C),E=Math.max(S,h.width),A=S+h.height+d,F=b.rectangle(-E/2,-A/2,E,A,{...C,fill:"transparent",stroke:"none"}),L=l.insert(()=>I,":first-child"),D=l.insert(()=>F);return k.attr("transform",`translate(${-w/2-N},${p?h.height/2+d/2-O/2-$:-h.height/2-d/2-O/2-$})`),k.attr("style",`color: ${x.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-A/2:A/2-h.height})`),L.attr("transform",`translate(0,${p?h.height/2+d/2:-h.height/2-d/2})`),tt(t,D),t.intersect=function(B){return R.info("iconSquare intersect",t,B),j.rect(t,B)},l}g(Qm,"iconCircle");async function Jm(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=et(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:f}=await ht(e,t,"icon-shape default"),d=t.pos==="t",p=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Fi(t),C=-m/2,v=-p/2,k=t.label?8:0,_=V.svg(l),w=K(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const O=b.get("fill");w.stroke=O??x;const N=_.path(ir(C,v,m,p,5),w),$=Math.max(m,h.width),S=p+h.height+k,I=_.rectangle(-$/2,-S/2,$,S,{...w,fill:"transparent",stroke:"none"}),E=l.insert(()=>N,":first-child").attr("class","icon-shape2"),A=l.insert(()=>I);if(t.icon){const F=l.append("g");F.html(`<g>${await xs(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const L=F.node().getBBox(),D=L.width,B=L.height,W=L.x,U=L.y;F.attr("transform",`translate(${-D/2-W},${d?h.height/2+k/2-B/2-U:-h.height/2-k/2-B/2-U})`),F.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-S/2:S/2-h.height})`),E.attr("transform",`translate(0,${d?h.height/2+k/2:-h.height/2-k/2})`),tt(t,A),t.intersect=function(F){if(R.info("iconSquare intersect",t,F),!t.label)return j.rect(t,F);const L=t.x??0,D=t.y??0,B=t.height??0;let W=[];return d?W=[{x:L-h.width/2,y:D-B/2},{x:L+h.width/2,y:D-B/2},{x:L+h.width/2,y:D-B/2+h.height+k},{x:L+m/2,y:D-B/2+h.height+k},{x:L+m/2,y:D+B/2},{x:L-m/2,y:D+B/2},{x:L-m/2,y:D-B/2+h.height+k},{x:L-h.width/2,y:D-B/2+h.height+k}]:W=[{x:L-m/2,y:D-B/2},{x:L+m/2,y:D-B/2},{x:L+m/2,y:D-B/2+p},{x:L+h.width/2,y:D-B/2+p},{x:L+h.width/2/2,y:D+B/2},{x:L-h.width/2,y:D+B/2},{x:L-h.width/2,y:D-B/2+p},{x:L-m/2,y:D-B/2+p}],j.polygon(t,W,F)},l}g(Jm,"iconRounded");async function t0(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=et(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:f}=await ht(e,t,"icon-shape default"),d=t.pos==="t",p=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Fi(t),C=-m/2,v=-p/2,k=t.label?8:0,_=V.svg(l),w=K(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const O=b.get("fill");w.stroke=O??x;const N=_.path(ir(C,v,m,p,.1),w),$=Math.max(m,h.width),S=p+h.height+k,I=_.rectangle(-$/2,-S/2,$,S,{...w,fill:"transparent",stroke:"none"}),E=l.insert(()=>N,":first-child"),A=l.insert(()=>I);if(t.icon){const F=l.append("g");F.html(`<g>${await xs(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const L=F.node().getBBox(),D=L.width,B=L.height,W=L.x,U=L.y;F.attr("transform",`translate(${-D/2-W},${d?h.height/2+k/2-B/2-U:-h.height/2-k/2-B/2-U})`),F.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-S/2:S/2-h.height})`),E.attr("transform",`translate(0,${d?h.height/2+k/2:-h.height/2-k/2})`),tt(t,A),t.intersect=function(F){if(R.info("iconSquare intersect",t,F),!t.label)return j.rect(t,F);const L=t.x??0,D=t.y??0,B=t.height??0;let W=[];return d?W=[{x:L-h.width/2,y:D-B/2},{x:L+h.width/2,y:D-B/2},{x:L+h.width/2,y:D-B/2+h.height+k},{x:L+m/2,y:D-B/2+h.height+k},{x:L+m/2,y:D+B/2},{x:L-m/2,y:D+B/2},{x:L-m/2,y:D-B/2+h.height+k},{x:L-h.width/2,y:D-B/2+h.height+k}]:W=[{x:L-m/2,y:D-B/2},{x:L+m/2,y:D-B/2},{x:L+m/2,y:D-B/2+p},{x:L+h.width/2,y:D-B/2+p},{x:L+h.width/2/2,y:D+B/2},{x:L-h.width/2,y:D+B/2},{x:L-h.width/2,y:D-B/2+p},{x:L-m/2,y:D-B/2+p}],j.polygon(t,W,F)},l}g(t0,"iconSquare");async function e0(e,t,{config:{flowchart:r}}){const i=new Image;i.src=(t==null?void 0:t.img)??"",await i.decode();const n=Number(i.naturalWidth.toString().replace("px","")),a=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=n/a;const{labelStyles:o}=et(t);t.labelStyle=o;const s=r==null?void 0:r.wrappingWidth;t.defaultWidth=r==null?void 0:r.wrappingWidth;const c=Math.max(t.label?s??0:0,(t==null?void 0:t.assetWidth)??n),l=t.constraint==="on"&&t!=null&&t.assetHeight?t.assetHeight*t.imageAspectRatio:c,h=t.constraint==="on"?l/t.imageAspectRatio:(t==null?void 0:t.assetHeight)??a;t.width=Math.max(l,s??0);const{shapeSvg:u,bbox:f,label:d}=await ht(e,t,"image-shape default"),p=t.pos==="t",m=-l/2,y=-h/2,x=t.label?8:0,b=V.svg(u),C=K(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=b.rectangle(m,y,l,h,C),k=Math.max(l,f.width),_=h+f.height+x,w=b.rectangle(-k/2,-_/2,k,_,{...C,fill:"none",stroke:"none"}),O=u.insert(()=>v,":first-child"),N=u.insert(()=>w);if(t.img){const $=u.append("image");$.attr("href",t.img),$.attr("width",l),$.attr("height",h),$.attr("preserveAspectRatio","none"),$.attr("transform",`translate(${-l/2},${p?_/2-h:-_/2})`)}return d.attr("transform",`translate(${-f.width/2-(f.x-(f.left??0))},${p?-h/2-f.height/2-x/2:h/2-f.height/2+x/2})`),O.attr("transform",`translate(0,${p?f.height/2+x/2:-f.height/2-x/2})`),tt(t,N),t.intersect=function($){if(R.info("iconSquare intersect",t,$),!t.label)return j.rect(t,$);const S=t.x??0,I=t.y??0,E=t.height??0;let A=[];return p?A=[{x:S-f.width/2,y:I-E/2},{x:S+f.width/2,y:I-E/2},{x:S+f.width/2,y:I-E/2+f.height+x},{x:S+l/2,y:I-E/2+f.height+x},{x:S+l/2,y:I+E/2},{x:S-l/2,y:I+E/2},{x:S-l/2,y:I-E/2+f.height+x},{x:S-f.width/2,y:I-E/2+f.height+x}]:A=[{x:S-l/2,y:I-E/2},{x:S+l/2,y:I-E/2},{x:S+l/2,y:I-E/2+h},{x:S+f.width/2,y:I-E/2+h},{x:S+f.width/2/2,y:I+E/2},{x:S-f.width/2,y:I+E/2},{x:S-f.width/2,y:I-E/2+h},{x:S-l/2,y:I-E/2+h}],j.polygon(t,A,$)},u}g(e0,"imageSquare");async function r0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=V.svg(n),f=K(t,{}),d=gt(c),p=u.path(d,f);l=n.insert(()=>p,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=nr(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,tt(t,l),t.intersect=function(u){return j.polygon(t,c,u)},n}g(r0,"inv_trapezoid");async function In(e,t,r){const{labelStyles:i,nodeStyles:n}=et(t);t.labelStyle=i;const{shapeSvg:a,bbox:o}=await ht(e,t,ot(t)),s=Math.max(o.width+r.labelPaddingX*2,(t==null?void 0:t.width)||0),c=Math.max(o.height+r.labelPaddingY*2,(t==null?void 0:t.height)||0),l=-s/2,h=-c/2;let u,{rx:f,ry:d}=t;const{cssStyles:p}=t;if(r!=null&&r.rx&&r.ry&&(f=r.rx,d=r.ry),t.look==="handDrawn"){const m=V.svg(a),y=K(t,{}),x=f||d?m.path(ir(l,h,s,c,f||0),y):m.rectangle(l,h,s,c,y);u=a.insert(()=>x,":first-child"),u.attr("class","basic label-container").attr("style",re(p))}else u=a.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",n).attr("rx",re(f)).attr("ry",re(d)).attr("x",l).attr("y",h).attr("width",s).attr("height",c);return tt(t,u),t.intersect=function(m){return j.rect(t,m)},a}g(In,"drawRect");async function i0(e,t){const{shapeSvg:r,bbox:i,label:n}=await ht(e,t,"label"),a=r.insert("rect",":first-child");return a.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),n.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),tt(t,a),t.intersect=function(c){return j.rect(t,c)},r}g(i0,"labelRect");async function n0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=V.svg(n),f=K(t,{}),d=gt(c),p=u.path(d,f);l=n.insert(()=>p,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=nr(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,tt(t,l),t.intersect=function(u){return j.polygon(t,c,u)},n}g(n0,"lean_left");async function a0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=V.svg(n),f=K(t,{}),d=gt(c),p=u.path(d,f);l=n.insert(()=>p,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=nr(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,tt(t,l),t.intersect=function(u){return j.polygon(t,c,u)},n}g(a0,"lean_right");function s0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.label="",t.labelStyle=r;const n=e.insert("g").attr("class",ot(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,o=Math.max(35,(t==null?void 0:t.width)??0),s=Math.max(35,(t==null?void 0:t.height)??0),c=7,l=[{x:o,y:0},{x:0,y:s+c/2},{x:o-2*c,y:s+c/2},{x:0,y:2*s},{x:o,y:s-c/2},{x:2*c,y:s-c/2}],h=V.svg(n),u=K(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=gt(l),d=h.path(f,u),p=n.insert(()=>d,":first-child");return a&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",a),i&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",i),p.attr("transform",`translate(-${o/2},${-s})`),tt(t,p),t.intersect=function(m){return R.info("lightningBolt intersect",t,m),j.polygon(t,l,m)},n}g(s0,"lightningBolt");var cL=g((e,t,r,i,n,a,o)=>[`M${e},${t+a}`,`a${n},${a} 0,0,0 ${r},0`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+a+o}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),hL=g((e,t,r,i,n,a,o)=>[`M${e},${t+a}`,`M${e+r},${t+a}`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+a+o}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),uL=g((e,t,r,i,n,a)=>[`M${e-r/2},${-i/2}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function o0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0),t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(a.height+l+(t.padding??0),t.height??0),u=h*.1;let f;const{cssStyles:d}=t;if(t.look==="handDrawn"){const p=V.svg(n),m=hL(0,0,s,h,c,l,u),y=uL(0,l,s,h,c,l),x=K(t,{}),b=p.path(m,x),C=p.path(y,x);n.insert(()=>C,":first-child").attr("class","line"),f=n.insert(()=>b,":first-child"),f.attr("class","basic label-container"),d&&f.attr("style",d)}else{const p=cL(0,0,s,h,c,l,u);f=n.insert("path",":first-child").attr("d",p).attr("class","basic label-container").attr("style",re(d)).attr("style",i)}return f.attr("label-offset-y",l),f.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),tt(t,f),o.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+l-(a.y-(a.top??0))})`),t.intersect=function(p){const m=j.rect(t,p),y=m.x-(t.x??0);if(c!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-l)){let x=l*l*(1-y*y/(c*c));x>0&&(x=Math.sqrt(x)),x=l-x,p.y-(t.y??0)>0&&(x=-x),m.y+=x}return m},n}g(o0,"linedCylinder");async function l0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,{cssStyles:u}=t,f=V.svg(n),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...ur(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,l,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=f.polygon(p.map(x=>[x.x,x.y]),d),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l/2-(a.y-(a.top??0))})`),tt(t,y),t.intersect=function(x){return j.polygon(t,p,x)},n}g(l0,"linedWaveEdgedRect");async function c0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:f}=t,d=V.svg(n),p=K(t,{}),m=[{x:h-l,y:u+l},{x:h-l,y:u+c+l},{x:h+s-l,y:u+c+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u+c-l},{x:h+s+l,y:u+c-l},{x:h+s+l,y:u-l},{x:h+l,y:u-l},{x:h+l,y:u},{x:h,y:u},{x:h,y:u+l}],y=[{x:h,y:u+l},{x:h+s-l,y:u+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const x=gt(m),b=d.path(x,p),C=gt(y),v=d.path(C,{...p,fill:"none"}),k=n.insert(()=>v,":first-child");return k.insert(()=>b,":first-child"),k.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(a.width/2)-l-(a.x-(a.left??0))}, ${-(a.height/2)+l-(a.y-(a.top??0))})`),tt(t,k),t.intersect=function(_){return j.polygon(t,m,_)},n}g(c0,"multiRect");async function h0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,u=-s/2,f=-h/2,d=5,{cssStyles:p}=t,m=ur(u-d,f+h+d,u+s-d,f+h+d,l,.8),y=m==null?void 0:m[m.length-1],x=[{x:u-d,y:f+d},{x:u-d,y:f+h+d},...m,{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:y.y-2*d},{x:u+s+d,y:y.y-2*d},{x:u+s+d,y:f-d},{x:u+d,y:f-d},{x:u+d,y:f},{x:u,y:f},{x:u,y:f+d}],b=[{x:u,y:f+d},{x:u+s-d,y:f+d},{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:f},{x:u,y:f}],C=V.svg(n),v=K(t,{});t.look!=="handDrawn"&&(v.roughness=0,v.fillStyle="solid");const k=gt(x),_=C.path(k,v),w=gt(b),O=C.path(w,v),N=n.insert(()=>_,":first-child");return N.insert(()=>O),N.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&N.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&N.selectAll("path").attr("style",i),N.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-(a.width/2)-d-(a.x-(a.left??0))}, ${-(a.height/2)+d-l/2-(a.y-(a.top??0))})`),tt(t,N),t.intersect=function($){return j.polygon(t,x,$)},n}g(h0,"multiWaveEdgedRectangle");async function u0(e,t,{config:{themeVariables:r}}){var x;const{labelStyles:i,nodeStyles:n}=et(t);t.labelStyle=i,t.useHtmlLabels||((x=he().flowchart)==null?void 0:x.htmlLabels)!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s}=await ht(e,t,ot(t)),c=Math.max(s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-c/2,u=-l/2,{cssStyles:f}=t,d=V.svg(o),p=K(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const m=d.rectangle(h,u,c,l,p),y=o.insert(()=>m,":first-child");return y.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",f),n&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",n),tt(t,y),t.intersect=function(b){return j.rect(t,b)},o}g(u0,"note");var fL=g((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function f0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=a.width+t.padding,s=a.height+t.padding,c=o+s,l=[{x:c/2,y:0},{x:c,y:-c/2},{x:c/2,y:-c},{x:0,y:-c/2}];let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const f=V.svg(n),d=K(t,{}),p=fL(0,0,c),m=f.path(p,d);h=n.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`),u&&h.attr("style",u)}else h=nr(n,c,c,l);return i&&h.attr("style",i),tt(t,h),t.intersect=function(f){return R.debug(`APA12 Intersect called SPLIT
point:`,f,`
node:
`,t,`
res:`,j.polygon(t,l,f)),j.polygon(t,l,f)},n}g(f0,"question");async function d0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,u=h/2,f=[{x:l+u,y:h},{x:l,y:0},{x:l+u,y:-h},{x:-l,y:-h},{x:-l,y:h}],{cssStyles:d}=t,p=V.svg(n),m=K(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=gt(f),x=p.path(y,m),b=n.insert(()=>x,":first-child");return b.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),b.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-a.width/2-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),tt(t,b),t.intersect=function(C){return j.polygon(t,f,C)},n}g(d0,"rect_left_inv_arrow");async function p0(e,t){var O,N;const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;let n;t.cssClasses?n="node "+t.cssClasses:n="node default";const a=e.insert("g").attr("class",n).attr("id",t.domId||t.id),o=a.insert("g"),s=a.insert("g").attr("class","label").attr("style",i),c=t.description,l=t.label,h=s.node().appendChild(await Sr(l,t.labelStyle,!0,!0));let u={width:0,height:0};if($t((N=(O=yt())==null?void 0:O.flowchart)==null?void 0:N.htmlLabels)){const $=h.children[0],S=pt(h);u=$.getBoundingClientRect(),S.attr("width",u.width),S.attr("height",u.height)}R.info("Text 2",c);const f=c||[],d=h.getBBox(),p=s.node().appendChild(await Sr(f.join?f.join("<br/>"):f,t.labelStyle,!0,!0)),m=p.children[0],y=pt(p);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const x=(t.padding||0)/2;pt(p).attr("transform","translate( "+(u.width>d.width?0:(d.width-u.width)/2)+", "+(d.height+x+5)+")"),pt(h).attr("transform","translate( "+(u.width<d.width?0:-(d.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-x+3)+")");const b=u.width+(t.padding||0),C=u.height+(t.padding||0),v=-u.width/2-x,k=-u.height/2-x;let _,w;if(t.look==="handDrawn"){const $=V.svg(a),S=K(t,{}),I=$.path(ir(v,k,b,C,t.rx||0),S),E=$.line(-u.width/2-x,-u.height/2-x+d.height+x,u.width/2+x,-u.height/2-x+d.height+x,S);w=a.insert(()=>(R.debug("Rough node insert CXC",I),E),":first-child"),_=a.insert(()=>(R.debug("Rough node insert CXC",I),I),":first-child")}else _=o.insert("rect",":first-child"),w=o.insert("line"),_.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-x).attr("y",-u.height/2-x).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),w.attr("class","divider").attr("x1",-u.width/2-x).attr("x2",u.width/2+x).attr("y1",-u.height/2-x+d.height+x).attr("y2",-u.height/2-x+d.height+x);return tt(t,_),t.intersect=function($){return j.rect(t,$)},a}g(p0,"rectWithTitle");async function g0(e,t){const r={rx:5,ry:5,labelPaddingX:((t==null?void 0:t.padding)||0)*1,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return In(e,t,r)}g(g0,"roundedRect");async function m0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=(t==null?void 0:t.padding)??0,c=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-a.width/2-s,u=-a.height/2-s,{cssStyles:f}=t,d=V.svg(n),p=K(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const m=[{x:h,y:u},{x:h+c+8,y:u},{x:h+c+8,y:u+l},{x:h-8,y:u+l},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),p),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container").attr("style",re(f)),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-c/2+4+(t.padding??0)-(a.x-(a.left??0))},${-l/2+(t.padding??0)-(a.y-(a.top??0))})`),tt(t,x),t.intersect=function(b){return j.rect(t,b)},n}g(m0,"shadedProcess");async function y0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,{cssStyles:u}=t,f=V.svg(n),d=K(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const p=[{x:l,y:h},{x:l,y:h+c},{x:l+s,y:h+c},{x:l+s,y:h-c/2}],m=gt(p),y=f.path(m,d),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),x.attr("transform",`translate(0, ${c/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))}, ${-c/4+(t.padding??0)-(a.y-(a.top??0))})`),tt(t,x),t.intersect=function(b){return j.polygon(t,p,b)},n}g(y0,"slopedRect");async function x0(e,t){const r={rx:0,ry:0,labelPaddingX:((t==null?void 0:t.padding)||0)*2,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return In(e,t,r)}g(x0,"squareRect");async function b0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=a.height+t.padding,s=a.width+o/4+t.padding;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=V.svg(n),u=K(t,{}),f=ir(-s/2,-o/2,s,o,o/2),d=h.path(f,u);c=n.insert(()=>d,":first-child"),c.attr("class","basic label-container").attr("style",re(l))}else c=n.insert("rect",":first-child"),c.attr("class","basic label-container").attr("style",i).attr("rx",o/2).attr("ry",o/2).attr("x",-s/2).attr("y",-o/2).attr("width",s).attr("height",o);return tt(t,c),t.intersect=function(h){return j.rect(t,h)},n}g(b0,"stadium");async function _0(e,t){return In(e,t,{rx:5,ry:5})}g(_0,"state");function C0(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:n}=et(t);t.labelStyle=i;const{cssStyles:a}=t,{lineColor:o,stateBorder:s,nodeBorder:c}=r,l=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=V.svg(l),u=K(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),d=s??c,p=h.circle(0,0,5,{...u,fill:d,stroke:d,strokeWidth:2,fillStyle:"solid"}),m=l.insert(()=>f,":first-child");return m.insert(()=>p),a&&m.selectAll("path").attr("style",a),n&&m.selectAll("path").attr("style",n),tt(t,m),t.intersect=function(y){return j.circle(t,7,y)},l}g(C0,"stateEnd");function w0(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let a;if(t.look==="handDrawn"){const s=V.svg(n).circle(0,0,14,jA(i));a=n.insert(()=>s),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else a=n.insert("circle",":first-child"),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return tt(t,a),t.intersect=function(o){return j.circle(t,7,o)},n}g(w0,"stateStart");async function k0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=((t==null?void 0:t.padding)||0)/2,s=a.width+t.padding,c=a.height+t.padding,l=-a.width/2-o,h=-a.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if(t.look==="handDrawn"){const f=V.svg(n),d=K(t,{}),p=f.rectangle(l-8,h,s+16,c,d),m=f.line(l,h,l,h+c,d),y=f.line(l+s,h,l+s,h+c,d);n.insert(()=>m,":first-child"),n.insert(()=>y,":first-child");const x=n.insert(()=>p,":first-child"),{cssStyles:b}=t;x.attr("class","basic label-container").attr("style",re(b)),tt(t,x)}else{const f=nr(n,s,c,u);i&&f.attr("style",i),tt(t,f)}return t.intersect=function(f){return j.polygon(t,u,f)},n}g(k0,"subroutine");async function v0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=-o/2,l=-s/2,h=.2*s,u=.2*s,{cssStyles:f}=t,d=V.svg(n),p=K(t,{}),m=[{x:c-h/2,y:l},{x:c+o+h/2,y:l},{x:c+o+h/2,y:l+s},{x:c-h/2,y:l+s}],y=[{x:c+o-h/2,y:l+s},{x:c+o+h/2,y:l+s},{x:c+o+h/2,y:l+s-u}];t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const x=gt(m),b=d.path(x,p),C=gt(y),v=d.path(C,{...p,fillStyle:"solid"}),k=n.insert(()=>v,":first-child");return k.insert(()=>b,":first-child"),k.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",i),tt(t,k),t.intersect=function(_){return j.polygon(t,m,_)},n}g(v0,"taggedRect");async function S0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=.2*s,u=.2*c,f=c+l,{cssStyles:d}=t,p=V.svg(n),m=K(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:f/2},...ur(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,l,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2}],x=-s/2+s/2*.1,b=-f/2-u*.4,C=[{x:x+s-h,y:(b+c)*1.4},{x:x+s,y:b+c-u},{x:x+s,y:(b+c)*.9},...ur(x+s,(b+c)*1.3,x+s-h,(b+c)*1.5,-c*.03,.5)],v=gt(y),k=p.path(v,m),_=gt(C),w=p.path(_,{...m,fillStyle:"solid"}),O=n.insert(()=>w,":first-child");return O.insert(()=>k,":first-child"),O.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",i),O.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l/2-(a.y-(a.top??0))})`),tt(t,O),t.intersect=function(N){return j.polygon(t,y,N)},n}g(S0,"taggedWaveEdgedRectangle");async function T0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=Math.max(a.width+t.padding,(t==null?void 0:t.width)||0),s=Math.max(a.height+t.padding,(t==null?void 0:t.height)||0),c=-o/2,l=-s/2,h=n.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",c).attr("y",l).attr("width",o).attr("height",s),tt(t,h),t.intersect=function(u){return j.rect(t,u)},n}g(T0,"text");var dL=g((e,t,r,i,n,a)=>`M${e},${t}
    a${n},${a} 0,0,1 0,${-i}
    l${r},0
    a${n},${a} 0,0,1 0,${i}
    M${r},${-i}
    a${n},${a} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),pL=g((e,t,r,i,n,a)=>[`M${e},${t}`,`M${e+r},${t}`,`a${n},${a} 0,0,0 0,${-i}`,`l${-r},0`,`a${n},${a} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),gL=g((e,t,r,i,n,a)=>[`M${e+r/2},${-i/2}`,`a${n},${a} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function M0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o,halfPadding:s}=await ht(e,t,ot(t)),c=t.look==="neo"?s*2:s,l=a.height+c,h=l/2,u=h/(2.5+l/50),f=a.width+u+c,{cssStyles:d}=t;let p;if(t.look==="handDrawn"){const m=V.svg(n),y=pL(0,0,f,l,u,h),x=gL(0,0,f,l,u,h),b=m.path(y,K(t,{})),C=m.path(x,K(t,{fill:"none"}));p=n.insert(()=>C,":first-child"),p=n.insert(()=>b,":first-child"),p.attr("class","basic label-container"),d&&p.attr("style",d)}else{const m=dL(0,0,f,l,u,h);p=n.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",re(d)).attr("style",i),p.attr("class","basic label-container"),d&&p.selectAll("path").attr("style",d),i&&p.selectAll("path").attr("style",i)}return p.attr("label-offset-x",u),p.attr("transform",`translate(${-f/2}, ${l/2} )`),o.attr("transform",`translate(${-(a.width/2)-u-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),tt(t,p),t.intersect=function(m){const y=j.rect(t,m),x=y.y-(t.y??0);if(h!=0&&(Math.abs(x)<(t.height??0)/2||Math.abs(x)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let b=u*u*(1-x*x/(h*h));b!=0&&(b=Math.sqrt(Math.abs(b))),b=u-b,m.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},n}g(M0,"tiltedCylinder");async function A0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=a.width+t.padding,s=a.height+t.padding,c=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=V.svg(n),f=K(t,{}),d=gt(c),p=u.path(d,f);l=n.insert(()=>p,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=nr(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,tt(t,l),t.intersect=function(u){return j.polygon(t,c,u)},n}g(A0,"trapezoid");async function L0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=60,s=20,c=Math.max(o,a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s,a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),{cssStyles:h}=t,u=V.svg(n),f=K(t,{});t.look!=="handDrawn"&&(f.roughness=0,f.fillStyle="solid");const d=[{x:-c/2*.8,y:-l/2},{x:c/2*.8,y:-l/2},{x:c/2,y:-l/2*.6},{x:c/2,y:l/2},{x:-c/2,y:l/2},{x:-c/2,y:-l/2*.6}],p=gt(d),m=u.path(p,f),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),tt(t,y),t.intersect=function(x){return j.polygon(t,d,x)},n}g(L0,"trapezoidalPentagon");async function B0(e,t){var b;const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=$t((b=yt().flowchart)==null?void 0:b.htmlLabels),c=a.width+(t.padding??0),l=c+a.height,h=c+a.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-l}],{cssStyles:f}=t,d=V.svg(n),p=K(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const m=gt(u),y=d.path(m,p),x=n.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return f&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),t.width=c,t.height=l,tt(t,x),o.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${l/2-(a.height+(t.padding??0)/(s?2:1)-(a.y-(a.top??0)))})`),t.intersect=function(C){return R.info("Triangle intersect",t,u,C),j.polygon(t,u,C)},n}g(B0,"triangle");async function E0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/8,h=c+l,{cssStyles:u}=t,d=70-s,p=d>0?d/2:0,m=V.svg(n),y=K(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const x=[{x:-s/2-p,y:h/2},...ur(-s/2-p,h/2,s/2+p,h/2,l,.8),{x:s/2+p,y:-h/2},{x:-s/2-p,y:-h/2}],b=gt(x),C=m.path(b,y),v=n.insert(()=>C,":first-child");return v.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l-(a.y-(a.top??0))})`),tt(t,v),t.intersect=function(k){return j.polygon(t,x,k)},n}g(E0,"waveEdgedRectangle");async function F0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await ht(e,t,ot(t)),o=100,s=50,c=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/l;let u=c,f=l;u>f*h?f=u/h:u=f*h,u=Math.max(u,o),f=Math.max(f,s);const d=Math.min(f*.2,f/4),p=f+d*2,{cssStyles:m}=t,y=V.svg(n),x=K(t,{});t.look!=="handDrawn"&&(x.roughness=0,x.fillStyle="solid");const b=[{x:-u/2,y:p/2},...ur(-u/2,p/2,u/2,p/2,d,1),{x:u/2,y:-p/2},...ur(u/2,-p/2,-u/2,-p/2,d,-1)],C=gt(b),v=y.path(C,x),k=n.insert(()=>v,":first-child");return k.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&k.selectAll("path").attr("style",i),tt(t,k),t.intersect=function(_){return j.polygon(t,b,_)},n}g(F0,"waveRectangle");async function $0(e,t){const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await ht(e,t,ot(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:f}=t,d=V.svg(n),p=K(t,{}),m=[{x:h-l,y:u-l},{x:h-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u-l}],y=`M${h-l},${u-l} L${h+s},${u-l} L${h+s},${u+c} L${h-l},${u+c} L${h-l},${u-l}
                M${h-l},${u} L${h+s},${u}
                M${h},${u-l} L${h},${u+c}`;t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const x=d.path(y,p),b=n.insert(()=>x,":first-child");return b.attr("transform",`translate(${l/2}, ${l/2})`),b.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(a.width/2)+l/2-(a.x-(a.left??0))}, ${-(a.height/2)+l/2-(a.y-(a.top??0))})`),tt(t,b),t.intersect=function(C){return j.polygon(t,m,C)},n}g($0,"windowPane");async function Ac(e,t){var W,U,X;const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:J}=he(),{background:it}=J,ut={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${it}`]};await Ac(e,ut)}const i=he();t.useHtmlLabels=i.htmlLabels;let n=((W=i.er)==null?void 0:W.diagramPadding)??10,a=((U=i.er)==null?void 0:U.entityPadding)??6;const{cssStyles:o}=t,{labelStyles:s}=et(t);if(r.attributes.length===0&&t.label){const J={rx:0,ry:0,labelPaddingX:n,labelPaddingY:n*1.5};er(t.label,i)+J.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const it=await In(e,t,J);if(!$t(i.htmlLabels)){const ut=it.select("text"),Q=(X=ut.node())==null?void 0:X.getBBox();ut.attr("transform",`translate(${-Q.width/2}, 0)`)}return it}i.htmlLabels||(n*=1.25,a*=1.25);let c=ot(t);c||(c="node default");const l=e.insert("g").attr("class",c).attr("id",t.domId||t.id),h=await Kr(l,t.label??"",i,0,0,["name"],s);h.height+=a;let u=0;const f=[];let d=0,p=0,m=0,y=0,x=!0,b=!0;for(const J of r.attributes){const it=await Kr(l,J.type,i,0,u,["attribute-type"],s);d=Math.max(d,it.width+n);const ut=await Kr(l,J.name,i,0,u,["attribute-name"],s);p=Math.max(p,ut.width+n);const Q=await Kr(l,J.keys.join(),i,0,u,["attribute-keys"],s);m=Math.max(m,Q.width+n);const kt=await Kr(l,J.comment,i,0,u,["attribute-comment"],s);y=Math.max(y,kt.width+n),u+=Math.max(it.height,ut.height,Q.height,kt.height)+a,f.push(u)}f.pop();let C=4;m<=n&&(x=!1,m=0,C--),y<=n&&(b=!1,y=0,C--);const v=l.node().getBBox();if(h.width+n*2-(d+p+m+y)>0){const J=h.width+n*2-(d+p+m+y);d+=J/C,p+=J/C,m>0&&(m+=J/C),y>0&&(y+=J/C)}const k=d+p+m+y,_=V.svg(l),w=K(t,{});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");const O=Math.max(v.width+n*2,(t==null?void 0:t.width)||0,k),N=Math.max(v.height+(f[0]||u)+a,(t==null?void 0:t.height)||0),$=-O/2,S=-N/2;l.selectAll("g:not(:first-child)").each((J,it,ut)=>{const Q=pt(ut[it]),kt=Q.attr("transform");let Tt=0,It=0;if(kt){const G=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(kt);G&&(Tt=parseFloat(G[1]),It=parseFloat(G[2]),Q.attr("class").includes("attribute-name")?Tt+=d:Q.attr("class").includes("attribute-keys")?Tt+=d+p:Q.attr("class").includes("attribute-comment")&&(Tt+=d+p+m))}Q.attr("transform",`translate(${$+n/2+Tt}, ${It+S+h.height+a/2})`)}),l.select(".name").attr("transform","translate("+-h.width/2+", "+(S+a/2)+")");const I=_.rectangle($,S,O,N,w),E=l.insert(()=>I,":first-child").attr("style",o.join("")),{themeVariables:A}=he(),{rowEven:F,rowOdd:L,nodeBorder:D}=A;f.push(0);for(const[J,it]of f.entries()){if(J===0&&f.length>1)continue;const ut=J%2===0&&it!==0,Q=_.rectangle($,h.height+S+it,O,h.height,{...w,fill:ut?F:L,stroke:D});l.insert(()=>Q,"g.label").attr("style",o.join("")).attr("class",`row-rect-${J%2===0?"even":"odd"}`)}let B=_.line($,h.height+S,O+$,h.height+S,w);l.insert(()=>B).attr("class","divider"),B=_.line(d+$,h.height+S,d+$,N+S,w),l.insert(()=>B).attr("class","divider"),x&&(B=_.line(d+p+$,h.height+S,d+p+$,N+S,w),l.insert(()=>B).attr("class","divider")),b&&(B=_.line(d+p+m+$,h.height+S,d+p+m+$,N+S,w),l.insert(()=>B).attr("class","divider"));for(const J of f)B=_.line($,h.height+S+J,O+$,h.height+S+J,w),l.insert(()=>B).attr("class","divider");return tt(t,E),t.intersect=function(J){return j.rect(t,J)},l}g(Ac,"erBox");async function Kr(e,t,r,i=0,n=0,a=[],o=""){const s=e.insert("g").attr("class",`label ${a.join(" ")}`).attr("transform",`translate(${i}, ${n})`).attr("style",o);t!==ch(t)&&(t=ch(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const c=s.node().appendChild(await dr(s,t,{width:er(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=c.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let l=c.getBBox();if($t(r.htmlLabels)){const h=c.children[0];h.style.textAlign="start";const u=pt(c);l=h.getBoundingClientRect(),u.attr("width",l.width),u.attr("height",l.height)}return l}g(Kr,"addText");async function D0(e,t,r,i,n=r.class.padding??12){const a=i?0:3,o=e.insert("g").attr("class",ot(t)).attr("id",t.domId||t.id);let s=null,c=null,l=null,h=null,u=0,f=0,d=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const b=t.annotations[0];await an(s,{text:`«${b}»`},0),u=s.node().getBBox().height}c=o.insert("g").attr("class","label-group text"),await an(c,t,0,["font-weight: bolder"]);const p=c.node().getBBox();f=p.height,l=o.insert("g").attr("class","members-group text");let m=0;for(const b of t.members){const C=await an(l,b,m,[b.parseClassifier()]);m+=C+a}d=l.node().getBBox().height,d<=0&&(d=n/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const b of t.methods){const C=await an(h,b,y,[b.parseClassifier()]);y+=C+a}let x=o.node().getBBox();if(s!==null){const b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return c.attr("transform",`translate(${-p.width/2}, ${u})`),x=o.node().getBBox(),l.attr("transform",`translate(0, ${u+f+n*2})`),x=o.node().getBBox(),h.attr("transform",`translate(0, ${u+f+(d?d+n*4:n*2)})`),x=o.node().getBBox(),{shapeSvg:o,bbox:x}}g(D0,"textHelper");async function an(e,t,r,i=[]){const n=e.insert("g").attr("class","label").attr("style",i.join("; ")),a=he();let o="useHtmlLabels"in t?t.useHtmlLabels:$t(a.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),xi(s)&&(o=!0);const c=await dr(n,$l(qr(s)),{width:er(s,a)+50,classes:"markdown-node-label",useHtmlLabels:o},a);let l,h=1;if(o){const u=c.children[0],f=pt(c);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const d=u.getElementsByTagName("img");if(d){const p=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...d].map(m=>new Promise(y=>{function x(){var b;if(m.style.display="flex",m.style.flexDirection="column",p){const C=((b=a.fontSize)==null?void 0:b.toString())??window.getComputedStyle(document.body).fontSize,k=parseInt(C,10)*5+"px";m.style.minWidth=k,m.style.maxWidth=k}else m.style.width="100%";y(m)}g(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}l=u.getBoundingClientRect(),f.attr("width",l.width),f.attr("height",l.height)}else{i.includes("font-weight: bolder")&&pt(c).selectAll("tspan").attr("font-weight",""),h=c.children.length;const u=c.children[0];(c.textContent===""||c.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),l=c.getBBox()}return n.attr("transform","translate(0,"+(-l.height/(2*h)+r)+")"),l.height}g(an,"addText");async function O0(e,t){var N,$;const r=yt(),i=r.class.padding??12,n=i,a=t.useHtmlLabels??$t(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:c}=await D0(e,t,r,a,n),{labelStyles:l,nodeStyles:h}=et(t);t.labelStyle=l,t.cssStyles=o.styles||"";const u=((N=o.styles)==null?void 0:N.join(";"))||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const f=o.members.length===0&&o.methods.length===0&&!(($=r.class)!=null&&$.hideEmptyMembersBox),d=V.svg(s),p=K(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const m=c.width;let y=c.height;o.members.length===0&&o.methods.length===0?y+=n:o.members.length>0&&o.methods.length===0&&(y+=n*2);const x=-m/2,b=-y/2,C=d.rectangle(x-i,b-i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(f?i*2:o.members.length===0&&o.methods.length===0?-i:0),p),v=s.insert(()=>C,":first-child");v.attr("class","basic label-container");const k=v.node().getBBox();s.selectAll(".text").each((S,I,E)=>{var W;const A=pt(E[I]),F=A.attr("transform");let L=0;if(F){const X=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(F);X&&(L=parseFloat(X[2]))}let D=L+b+i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0);a||(D-=4);let B=x;(A.attr("class").includes("label-group")||A.attr("class").includes("annotation-group"))&&(B=-((W=A.node())==null?void 0:W.getBBox().width)/2||0,s.selectAll("text").each(function(U,X,J){window.getComputedStyle(J[X]).textAnchor==="middle"&&(B=0)})),A.attr("transform",`translate(${B}, ${D})`)});const _=s.select(".annotation-group").node().getBBox().height-(f?i/2:0)||0,w=s.select(".label-group").node().getBBox().height-(f?i/2:0)||0,O=s.select(".members-group").node().getBBox().height-(f?i/2:0)||0;if(o.members.length>0||o.methods.length>0||f){const S=d.line(k.x,_+w+b+i,k.x+k.width,_+w+b+i,p);s.insert(()=>S).attr("class","divider").attr("style",u)}if(f||o.members.length>0||o.methods.length>0){const S=d.line(k.x,_+w+O+b+n*2+i,k.x+k.width,_+w+O+b+i+n*2,p);s.insert(()=>S).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),v.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!a){const S=RegExp(/color\s*:\s*([^;]*)/),I=S.exec(u);if(I){const E=I[0].replace("color","fill");s.selectAll("tspan").attr("style",E)}else if(l){const E=S.exec(l);if(E){const A=E[0].replace("color","fill");s.selectAll("tspan").attr("style",A)}}}return tt(t,v),t.intersect=function(S){return j.rect(t,S)},s}g(O0,"classBox");async function R0(e,t){var _,w;const{labelStyles:r,nodeStyles:i}=et(t);t.labelStyle=r;const n=t,a=t,o=20,s=20,c="verifyMethod"in t,l=ot(t),h=e.insert("g").attr("class",l).attr("id",t.domId??t.id);let u;c?u=await Fe(h,`&lt;&lt;${n.type}&gt;&gt;`,0,t.labelStyle):u=await Fe(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let f=u;const d=await Fe(h,n.name,f,t.labelStyle+"; font-weight: bold;");if(f+=d+s,c){const O=await Fe(h,`${n.requirementId?`Id: ${n.requirementId}`:""}`,f,t.labelStyle);f+=O;const N=await Fe(h,`${n.text?`Text: ${n.text}`:""}`,f,t.labelStyle);f+=N;const $=await Fe(h,`${n.risk?`Risk: ${n.risk}`:""}`,f,t.labelStyle);f+=$,await Fe(h,`${n.verifyMethod?`Verification: ${n.verifyMethod}`:""}`,f,t.labelStyle)}else{const O=await Fe(h,`${a.type?`Type: ${a.type}`:""}`,f,t.labelStyle);f+=O,await Fe(h,`${a.docRef?`Doc Ref: ${a.docRef}`:""}`,f,t.labelStyle)}const p=(((_=h.node())==null?void 0:_.getBBox().width)??200)+o,m=(((w=h.node())==null?void 0:w.getBBox().height)??200)+o,y=-p/2,x=-m/2,b=V.svg(h),C=K(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=b.rectangle(y,x,p,m,C),k=h.insert(()=>v,":first-child");if(k.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((O,N,$)=>{const S=pt($[N]),I=S.attr("transform");let E=0,A=0;if(I){const B=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(I);B&&(E=parseFloat(B[1]),A=parseFloat(B[2]))}const F=A-m/2;let L=y+o/2;(N===0||N===1)&&(L=E),S.attr("transform",`translate(${L}, ${F+o})`)}),f>u+d+s){const O=b.line(y,x+u+d+s,y+p,x+u+d+s,C);h.insert(()=>O).attr("style",i)}return tt(t,k),t.intersect=function(O){return j.rect(t,O)},h}g(R0,"requirementBox");async function Fe(e,t,r,i=""){if(t==="")return 0;const n=e.insert("g").attr("class","label").attr("style",i),a=yt(),o=a.htmlLabels??!0,s=await dr(n,$l(qr(t)),{width:er(t,a)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},a);let c;if(o){const l=s.children[0],h=pt(s);c=l.getBoundingClientRect(),h.attr("width",c.width),h.attr("height",c.height)}else{const l=s.children[0];for(const h of l.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);c=s.getBBox(),c.height+=6}return n.attr("transform",`translate(${-c.width/2},${-c.height/2+r})`),c.height}g(Fe,"addText");var mL=g(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function I0(e,t,{config:r}){var I,E;const{labelStyles:i,nodeStyles:n}=et(t);t.labelStyle=i||"";const a=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:c,label:l}=await ht(e,t,ot(t)),h=t.padding||10;let u="",f;"ticket"in t&&t.ticket&&((I=r==null?void 0:r.kanban)!=null&&I.ticketBaseUrl)&&(u=(E=r==null?void 0:r.kanban)==null?void 0:E.ticketBaseUrl.replace("#TICKET#",t.ticket),f=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const d={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let p,m;f?{label:p,bbox:m}=await vo(f,"ticket"in t&&t.ticket||"",d):{label:p,bbox:m}=await vo(s,"ticket"in t&&t.ticket||"",d);const{label:y,bbox:x}=await vo(s,"assigned"in t&&t.assigned||"",d);t.width=o;const b=10,C=(t==null?void 0:t.width)||0,v=Math.max(m.height,x.height)/2,k=Math.max(c.height+b*2,(t==null?void 0:t.height)||0)+v,_=-C/2,w=-k/2;l.attr("transform","translate("+(h-C/2)+", "+(-v-c.height/2)+")"),p.attr("transform","translate("+(h-C/2)+", "+(-v+c.height/2)+")"),y.attr("transform","translate("+(h+C/2-x.width-2*a)+", "+(-v+c.height/2)+")");let O;const{rx:N,ry:$}=t,{cssStyles:S}=t;if(t.look==="handDrawn"){const A=V.svg(s),F=K(t,{}),L=N||$?A.path(ir(_,w,C,k,N||0),F):A.rectangle(_,w,C,k,F);O=s.insert(()=>L,":first-child"),O.attr("class","basic label-container").attr("style",S||null)}else{O=s.insert("rect",":first-child"),O.attr("class","basic label-container __APA__").attr("style",n).attr("rx",N??5).attr("ry",$??5).attr("x",_).attr("y",w).attr("width",C).attr("height",k);const A="priority"in t&&t.priority;if(A){const F=s.append("line"),L=_+2,D=w+Math.floor((N??0)/2),B=w+k-Math.floor((N??0)/2);F.attr("x1",L).attr("y1",D).attr("x2",L).attr("y2",B).attr("stroke-width","4").attr("stroke",mL(A))}}return tt(t,O),t.height=k,t.intersect=function(A){return j.rect(t,A)},s}g(I0,"kanbanItem");var yL=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:x0},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:g0},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:b0},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:k0},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:qm},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:Om},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:f0},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:Xm},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:a0},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:n0},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:A0},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:r0},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:Um},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:T0},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:$m},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:m0},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:w0},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:C0},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:Gm},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:Zm},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:Pm},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:Nm},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:zm},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:s0},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:E0},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:Vm},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:M0},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:o0},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:Wm},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:Hm},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:B0},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:$0},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:Ym},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:L0},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:jm},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:y0},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:h0},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:c0},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:Fm},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:Im},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:S0},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:v0},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:F0},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:d0},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:l0}],xL=g(()=>{const t=[...Object.entries({state:_0,choice:Dm,note:u0,rectWithTitle:p0,labelRect:i0,iconSquare:t0,iconCircle:Qm,icon:Km,iconRounded:Jm,imageSquare:e0,anchor:Em,kanbanItem:I0,classBox:O0,erBox:Ac,requirementBox:R0}),...yL.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(n=>[n,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),P0=xL();function bL(e){return e in P0}g(bL,"isValidShape");var Ns=new Map;async function N0(e,t,r){let i,n;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const a=t.shape?P0[t.shape]:void 0;if(!a)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),n=await a(i,t,r)}else n=await a(e,t,r),i=n;return t.tooltip&&n.attr("title",t.tooltip),Ns.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}g(N0,"insertNode");var _3=g((e,t)=>{Ns.set(t.id,e)},"setNodeElem"),C3=g(()=>{Ns.clear()},"clear"),w3=g(e=>{const t=Ns.get(e.id);R.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),_L=g((e,t,r,i,n,a)=>{t.arrowTypeStart&&Ou(e,"start",t.arrowTypeStart,r,i,n,a),t.arrowTypeEnd&&Ou(e,"end",t.arrowTypeEnd,r,i,n,a)},"addEdgeMarkers"),CL={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},Ou=g((e,t,r,i,n,a,o)=>{var u;const s=CL[r];if(!s){R.warn(`Unknown arrow type: ${r}`);return}const c=s.type,h=`${n}_${a}-${c}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const f=o.replace(/[^\dA-Za-z]/g,"_"),d=`${h}_${f}`;if(!document.getElementById(d)){const p=document.getElementById(h);if(p){const m=p.cloneNode(!0);m.id=d,m.querySelectorAll("path, circle, line").forEach(x=>{x.setAttribute("stroke",o),s.fill&&x.setAttribute("fill",o)}),(u=p.parentNode)==null||u.appendChild(m)}}e.attr(`marker-${t}`,`url(${i}#${d})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),hs=new Map,qt=new Map,k3=g(()=>{hs.clear(),qt.clear()},"clear"),Qi=g(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),wL=g(async(e,t)=>{let r=$t(yt().flowchart.htmlLabels);const i=await dr(e,t.label,{style:Qi(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});R.info("abc82",t,t.labelType);const n=e.insert("g").attr("class","edgeLabel"),a=n.insert("g").attr("class","label");a.node().appendChild(i);let o=i.getBBox();if(r){const c=i.children[0],l=pt(i);o=c.getBoundingClientRect(),l.attr("width",o.width),l.attr("height",o.height)}a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),hs.set(t.id,n),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const c=await Sr(t.startLabelLeft,Qi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),qt.get(t.id)||qt.set(t.id,{}),qt.get(t.id).startLeft=l,sn(s,t.startLabelLeft)}if(t.startLabelRight){const c=await Sr(t.startLabelRight,Qi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=l.node().appendChild(c),h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),qt.get(t.id)||qt.set(t.id,{}),qt.get(t.id).startRight=l,sn(s,t.startLabelRight)}if(t.endLabelLeft){const c=await Sr(t.endLabelLeft,Qi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),qt.get(t.id)||qt.set(t.id,{}),qt.get(t.id).endLeft=l,sn(s,t.endLabelLeft)}if(t.endLabelRight){const c=await Sr(t.endLabelRight,Qi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),qt.get(t.id)||qt.set(t.id,{}),qt.get(t.id).endRight=l,sn(s,t.endLabelRight)}return i},"insertEdgeLabel");function sn(e,t){yt().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}g(sn,"setTerminalWidth");var kL=g((e,t)=>{R.debug("Moving label abc88 ",e.id,e.label,hs.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=yt(),{subGraphTitleTotalMargin:n}=Wl(i);if(e.label){const a=hs.get(e.id);let o=e.x,s=e.y;if(r){const c=De.calcLabelPosition(r);R.debug("Moving label "+e.label+" from (",o,",",s,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(o=c.x,s=c.y)}a.attr("transform",`translate(${o}, ${s+n/2})`)}if(e.startLabelLeft){const a=qt.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const c=De.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const a=qt.get(e.id).startRight;let o=e.x,s=e.y;if(r){const c=De.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const a=qt.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const c=De.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const a=qt.get(e.id).endRight;let o=e.x,s=e.y;if(r){const c=De.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),vL=g((e,t)=>{const r=e.x,i=e.y,n=Math.abs(t.x-r),a=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return n>=o||a>=s},"outsideNode"),SL=g((e,t,r)=>{R.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,n=e.y,a=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-a:o+a;const c=e.height/2,l=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(n-t.y)*o>Math.abs(i-t.x)*c){let u=r.y<t.y?t.y-c-n:n-c-t.y;s=h*u/l;const f={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+l-u:r.y-l+u};return s===0&&(f.x=t.x,f.y=t.y),h===0&&(f.x=t.x),l===0&&(f.y=t.y),R.debug(`abc89 top/bottom calc, Q ${l}, q ${u}, R ${h}, r ${s}`,f),f}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=l*s/h,f=r.x<t.x?r.x+h-s:r.x-h+s,d=r.y<t.y?r.y+u:r.y-u;return R.debug(`sides calc abc89, Q ${l}, q ${u}, R ${h}, r ${s}`,{_x:f,_y:d}),s===0&&(f=t.x,d=t.y),h===0&&(f=t.x),l===0&&(d=t.y),{x:f,y:d}}},"intersection"),Ru=g((e,t)=>{R.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],n=!1;return e.forEach(a=>{if(R.info("abc88 checking point",a,t),!vL(t,a)&&!n){const o=SL(t,i,a);R.debug("abc88 inside",a,i,o),R.debug("abc88 intersection",o,t);let s=!1;r.forEach(c=>{s=s||c.x===o.x&&c.y===o.y}),r.some(c=>c.x===o.x&&c.y===o.y)?R.warn("abc88 no intersect",o,r):r.push(o),n=!0}else R.warn("abc88 outside",a,i),i=a,n||r.push(a)}),R.debug("returning points",r),r},"cutPathAtIntersect");function z0(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const n=e[i-1],a=e[i],o=e[i+1];(n.x===a.x&&a.y===o.y&&Math.abs(a.x-o.x)>5&&Math.abs(a.y-n.y)>5||n.y===a.y&&a.x===o.x&&Math.abs(a.x-n.x)>5&&Math.abs(a.y-o.y)>5)&&(t.push(a),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}g(z0,"extractCornerPoints");var Iu=g(function(e,t,r){const i=t.x-e.x,n=t.y-e.y,a=Math.sqrt(i*i+n*n),o=r/a;return{x:t.x-o*i,y:t.y-o*n}},"findAdjacentPoint"),TL=g(function(e){const{cornerPointPositions:t}=z0(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const n=e[i-1],a=e[i+1],o=e[i],s=Iu(n,o,5),c=Iu(a,o,5),l=c.x-s.x,h=c.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let f={x:o.x,y:o.y};if(Math.abs(a.x-n.x)>10&&Math.abs(a.y-n.y)>=10){R.debug("Corner point fixing",Math.abs(a.x-n.x),Math.abs(a.y-n.y));const d=5;o.x===s.x?f={x:l<0?s.x-d+u:s.x+d-u,y:h<0?s.y-u:s.y+u}:f={x:l<0?s.x-u:s.x+u,y:h<0?s.y-d+u:s.y+d-u}}else R.debug("Corner point skipping fixing",Math.abs(a.x-n.x),Math.abs(a.y-n.y));r.push(f,c)}else r.push(e[i]);return r},"fixCorners"),ML=g(function(e,t,r,i,n,a,o){var N;const{handDrawnSeed:s}=yt();let c=t.points,l=!1;const h=n;var u=a;const f=[];for(const $ in t.cssCompiledStyles)wm($)||f.push(t.cssCompiledStyles[$]);u.intersect&&h.intersect&&(c=c.slice(1,t.points.length-1),c.unshift(h.intersect(c[0])),R.debug("Last point APA12",t.start,"-->",t.end,c[c.length-1],u,u.intersect(c[c.length-1])),c.push(u.intersect(c[c.length-1]))),t.toCluster&&(R.info("to cluster abc88",r.get(t.toCluster)),c=Ru(t.points,r.get(t.toCluster).node),l=!0),t.fromCluster&&(R.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(c,null,2)),c=Ru(c.reverse(),r.get(t.fromCluster).node).reverse(),l=!0);let d=c.filter($=>!Number.isNaN($.y));d=TL(d);let p=ma;switch(p=Va,t.curve){case"linear":p=Va;break;case"basis":p=ma;break;case"cardinal":p=cg;break;case"bumpX":p=ng;break;case"bumpY":p=ag;break;case"catmullRom":p=ug;break;case"monotoneX":p=yg;break;case"monotoneY":p=xg;break;case"natural":p=_g;break;case"step":p=Cg;break;case"stepAfter":p=kg;break;case"stepBefore":p=wg;break;default:p=ma}const{x:m,y}=S1(t),x=qv().x(m).y(y).curve(p);let b;switch(t.thickness){case"normal":b="edge-thickness-normal";break;case"thick":b="edge-thickness-thick";break;case"invisible":b="edge-thickness-invisible";break;default:b="edge-thickness-normal"}switch(t.pattern){case"solid":b+=" edge-pattern-solid";break;case"dotted":b+=" edge-pattern-dotted";break;case"dashed":b+=" edge-pattern-dashed";break;default:b+=" edge-pattern-solid"}let C,v=x(d);const k=Array.isArray(t.style)?t.style:[t.style];let _=k.find($=>$==null?void 0:$.startsWith("stroke:"));if(t.look==="handDrawn"){const $=V.svg(e);Object.assign([],d);const S=$.path(v,{roughness:.3,seed:s});b+=" transition",C=pt(S).select("path").attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")).attr("style",k?k.reduce((E,A)=>E+";"+A,""):"");let I=C.attr("d");C.attr("d",I),e.node().appendChild(C.node())}else{const $=f.join(";"),S=k?k.reduce((A,F)=>A+F+";",""):"";let I="";t.animate&&(I=" edge-animation-fast"),t.animation&&(I=" edge-animation-"+t.animation);const E=$?$+";"+S+";":S;C=e.append("path").attr("d",v).attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")+(I??"")).attr("style",E),_=(N=E.match(/stroke:([^;]+)/))==null?void 0:N[1]}let w="";(yt().flowchart.arrowMarkerAbsolute||yt().state.arrowMarkerAbsolute)&&(w=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,w=w.replace(/\(/g,"\\(").replace(/\)/g,"\\)")),R.info("arrowTypeStart",t.arrowTypeStart),R.info("arrowTypeEnd",t.arrowTypeEnd),_L(C,t,w,o,i,_);let O={};return l&&(O.updatedPath=c),O.originalPath=t.points,O},"insertEdge"),AL=g((e,t,r,i)=>{t.forEach(n=>{UL[n](e,r,i)})},"insertMarkers"),LL=g((e,t,r)=>{R.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),BL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),EL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),FL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),$L=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),DL=g((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),OL=g((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),RL=g((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),IL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),PL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),NL=g((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const n=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");n.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),n.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),zL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),WL=g((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const n=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");n.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),n.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),qL=g((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),HL=g((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsStart").attr("refX",0).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",9).attr("fill","none"),i.append("line").attr("x1",1).attr("x2",19).attr("y1",10).attr("y2",10),i.append("line").attr("y1",1).attr("y2",19).attr("x1",10).attr("x2",10)},"requirement_contains"),UL={extension:LL,composition:BL,aggregation:EL,dependency:FL,lollipop:$L,point:DL,circle:OL,cross:RL,barb:IL,only_one:PL,zero_or_one:NL,one_or_more:zL,zero_or_more:WL,requirement_arrow:qL,requirement_contains:HL},YL=AL,jL={common:Ai,getConfig:he,insertCluster:tL,insertEdge:ML,insertEdgeLabel:wL,insertMarkers:YL,insertNode:N0,interpolateToCurve:cc,labelHelper:ht,log:R,positionEdgeLabel:kL},Sn={},W0=g(e=>{for(const t of e)Sn[t.name]=t},"registerLayoutLoaders"),GL=g(()=>{W0([{name:"dagre",loader:g(async()=>await _t(()=>import("./dagre-OKDRZEBW-g_0ATcVB.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])),"loader")}])},"registerDefaultLayoutLoaders");GL();var v3=g(async(e,t)=>{if(!(e.layoutAlgorithm in Sn))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=Sn[e.layoutAlgorithm];return(await r.loader()).render(e,t,jL,{algorithm:r.algorithm})},"render"),S3=g((e="",{fallback:t="dagre"}={})=>{if(e in Sn)return e;if(t in Sn)return R.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),Pu={version:"11.6.0"},VL=g(e=>{var n;const{securityLevel:t}=yt();let r=pt("body");if(t==="sandbox"){const o=((n=pt(`#i${e}`).node())==null?void 0:n.contentDocument)??document;r=pt(o.body)}return r.select(`#${e}`)},"selectSvgElement"),q0="comm",H0="rule",U0="decl",XL="@import",ZL="@namespace",KL="@keyframes",QL="@layer",Y0=Math.abs,Lc=String.fromCharCode;function j0(e){return e.trim()}function ba(e,t,r){return e.replace(t,r)}function JL(e,t,r){return e.indexOf(t,r)}function ai(e,t){return e.charCodeAt(t)|0}function Ti(e,t,r){return e.slice(t,r)}function $e(e){return e.length}function tB(e){return e.length}function ia(e,t){return t.push(e),e}var zs=1,Mi=1,G0=0,_e=0,Bt=0,$i="";function Bc(e,t,r,i,n,a,o,s){return{value:e,root:t,parent:r,type:i,props:n,children:a,line:zs,column:Mi,length:o,return:"",siblings:s}}function eB(){return Bt}function rB(){return Bt=_e>0?ai($i,--_e):0,Mi--,Bt===10&&(Mi=1,zs--),Bt}function Se(){return Bt=_e<G0?ai($i,_e++):0,Mi++,Bt===10&&(Mi=1,zs++),Bt}function sr(){return ai($i,_e)}function _a(){return _e}function Ws(e,t){return Ti($i,e,t)}function Tn(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function iB(e){return zs=Mi=1,G0=$e($i=e),_e=0,[]}function nB(e){return $i="",e}function So(e){return j0(Ws(_e-1,xl(e===91?e+2:e===40?e+1:e)))}function aB(e){for(;(Bt=sr())&&Bt<33;)Se();return Tn(e)>2||Tn(Bt)>3?"":" "}function sB(e,t){for(;--t&&Se()&&!(Bt<48||Bt>102||Bt>57&&Bt<65||Bt>70&&Bt<97););return Ws(e,_a()+(t<6&&sr()==32&&Se()==32))}function xl(e){for(;Se();)switch(Bt){case e:return _e;case 34:case 39:e!==34&&e!==39&&xl(Bt);break;case 40:e===41&&xl(e);break;case 92:Se();break}return _e}function oB(e,t){for(;Se()&&e+Bt!==57;)if(e+Bt===84&&sr()===47)break;return"/*"+Ws(t,_e-1)+"*"+Lc(e===47?e:Se())}function lB(e){for(;!Tn(sr());)Se();return Ws(e,_e)}function cB(e){return nB(Ca("",null,null,null,[""],e=iB(e),0,[0],e))}function Ca(e,t,r,i,n,a,o,s,c){for(var l=0,h=0,u=o,f=0,d=0,p=0,m=1,y=1,x=1,b=0,C="",v=n,k=a,_=i,w=C;y;)switch(p=b,b=Se()){case 40:if(p!=108&&ai(w,u-1)==58){JL(w+=ba(So(b),"&","&\f"),"&\f",Y0(l?s[l-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:w+=So(b);break;case 9:case 10:case 13:case 32:w+=aB(p);break;case 92:w+=sB(_a()-1,7);continue;case 47:switch(sr()){case 42:case 47:ia(hB(oB(Se(),_a()),t,r,c),c),(Tn(p||1)==5||Tn(sr()||1)==5)&&$e(w)&&Ti(w,-1,void 0)!==" "&&(w+=" ");break;default:w+="/"}break;case 123*m:s[l++]=$e(w)*x;case 125*m:case 59:case 0:switch(b){case 0:case 125:y=0;case 59+h:x==-1&&(w=ba(w,/\f/g,"")),d>0&&($e(w)-u||m===0&&p===47)&&ia(d>32?zu(w+";",i,r,u-1,c):zu(ba(w," ","")+";",i,r,u-2,c),c);break;case 59:w+=";";default:if(ia(_=Nu(w,t,r,l,h,n,s,C,v=[],k=[],u,a),a),b===123)if(h===0)Ca(w,t,_,_,v,a,u,s,k);else{switch(f){case 99:if(ai(w,3)===110)break;case 108:if(ai(w,2)===97)break;default:h=0;case 100:case 109:case 115:}h?Ca(e,_,_,i&&ia(Nu(e,_,_,0,0,n,s,C,n,v=[],u,k),k),n,k,u,s,i?v:k):Ca(w,_,_,_,[""],k,0,s,k)}}l=h=d=0,m=x=1,C=w="",u=o;break;case 58:u=1+$e(w),d=p;default:if(m<1){if(b==123)--m;else if(b==125&&m++==0&&rB()==125)continue}switch(w+=Lc(b),b*m){case 38:x=h>0?1:(w+="\f",-1);break;case 44:s[l++]=($e(w)-1)*x,x=1;break;case 64:sr()===45&&(w+=So(Se())),f=sr(),h=u=$e(C=w+=lB(_a())),b++;break;case 45:p===45&&$e(w)==2&&(m=0)}}return a}function Nu(e,t,r,i,n,a,o,s,c,l,h,u){for(var f=n-1,d=n===0?a:[""],p=tB(d),m=0,y=0,x=0;m<i;++m)for(var b=0,C=Ti(e,f+1,f=Y0(y=o[m])),v=e;b<p;++b)(v=j0(y>0?d[b]+" "+C:ba(C,/&\f/g,d[b])))&&(c[x++]=v);return Bc(e,t,r,n===0?H0:s,c,l,h,u)}function hB(e,t,r,i){return Bc(e,t,r,q0,Lc(eB()),Ti(e,2,-2),0,i)}function zu(e,t,r,i,n){return Bc(e,t,r,U0,Ti(e,0,i),Ti(e,i+1,-1),i,n)}function bl(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function uB(e,t,r,i){switch(e.type){case QL:if(e.children.length)break;case XL:case ZL:case U0:return e.return=e.return||e.value;case q0:return"";case KL:return e.return=e.value+"{"+bl(e.children,i)+"}";case H0:if(!$e(e.value=e.props.join(",")))return""}return $e(r=bl(e.children,i))?e.return=e.value+"{"+r+"}":""}var fB=Mg(Object.keys,Object),dB=Object.prototype,pB=dB.hasOwnProperty;function gB(e){if(!Es(e))return fB(e);var t=[];for(var r in Object(e))pB.call(e,r)&&r!="constructor"&&t.push(r);return t}var _l=Wr(Ne,"DataView"),Cl=Wr(Ne,"Promise"),wl=Wr(Ne,"Set"),kl=Wr(Ne,"WeakMap"),Wu="[object Map]",mB="[object Object]",qu="[object Promise]",Hu="[object Set]",Uu="[object WeakMap]",Yu="[object DataView]",yB=zr(_l),xB=zr(vn),bB=zr(Cl),_B=zr(wl),CB=zr(kl),_r=Li;(_l&&_r(new _l(new ArrayBuffer(1)))!=Yu||vn&&_r(new vn)!=Wu||Cl&&_r(Cl.resolve())!=qu||wl&&_r(new wl)!=Hu||kl&&_r(new kl)!=Uu)&&(_r=function(e){var t=Li(e),r=t==mB?e.constructor:void 0,i=r?zr(r):"";if(i)switch(i){case yB:return Yu;case xB:return Wu;case bB:return qu;case _B:return Hu;case CB:return Uu}return t});var wB="[object Map]",kB="[object Set]",vB=Object.prototype,SB=vB.hasOwnProperty;function ju(e){if(e==null)return!0;if(Fs(e)&&(es(e)||typeof e=="string"||typeof e.splice=="function"||oc(e)||lc(e)||ts(e)))return!e.length;var t=_r(e);if(t==wB||t==kB)return!e.size;if(Es(e))return!gB(e).length;for(var r in e)if(SB.call(e,r))return!1;return!0}var V0="c4",TB=g(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),MB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./c4Diagram-VJAJSXHY-szasl701.js");return{diagram:t}},__vite__mapDeps([12,13,6,7,8,9,10,11]));return{id:V0,diagram:e}},"loader"),AB={id:V0,detector:TB,loader:MB},LB=AB,X0="flowchart",BB=g((e,t)=>{var r,i;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-wrapper"||((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"?!1:/^\s*graph/.test(e)},"detector"),EB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BdHPhGfE.js");return{diagram:t}},__vite__mapDeps([14,15,6,7,8,9,10,11]));return{id:X0,diagram:e}},"loader"),FB={id:X0,detector:BB,loader:EB},$B=FB,Z0="flowchart-v2",DB=g((e,t)=>{var r,i,n;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-d3"?!1:(((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&((n=t==null?void 0:t.flowchart)==null?void 0:n.defaultRenderer)==="dagre-wrapper"?!0:/^\s*flowchart/.test(e))},"detector"),OB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BdHPhGfE.js");return{diagram:t}},__vite__mapDeps([14,15,6,7,8,9,10,11]));return{id:Z0,diagram:e}},"loader"),RB={id:Z0,detector:DB,loader:OB},IB=RB,K0="er",PB=g(e=>/^\s*erDiagram/.test(e),"detector"),NB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./erDiagram-Q7BY3M3F-DLPiuuPL.js");return{diagram:t}},__vite__mapDeps([16,15,6,7,8,9,10,11]));return{id:K0,diagram:e}},"loader"),zB={id:K0,detector:PB,loader:NB},WB=zB,Q0="gitGraph",qB=g(e=>/^\s*gitGraph/.test(e),"detector"),HB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./gitGraphDiagram-7IBYFJ6S-D24Y9tf4.js");return{diagram:t}},__vite__mapDeps([17,18,19,20,6,7,8,9,10,11,2,4,5]));return{id:Q0,diagram:e}},"loader"),UB={id:Q0,detector:qB,loader:HB},YB=UB,J0="gantt",jB=g(e=>/^\s*gantt/.test(e),"detector"),GB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./ganttDiagram-APWFNJXF-DXTK-VH-.js");return{diagram:t}},__vite__mapDeps([21,7,6,8,9,10,11]));return{id:J0,diagram:e}},"loader"),VB={id:J0,detector:jB,loader:GB},XB=VB,ty="info",ZB=g(e=>/^\s*info/.test(e),"detector"),KB=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./infoDiagram-PH2N3AL5-j82a-EEt.js");return{diagram:t}},__vite__mapDeps([22,20,6,7,8,9,10,11,2,4,5]));return{id:ty,diagram:e}},"loader"),QB={id:ty,detector:ZB,loader:KB},ey="pie",JB=g(e=>/^\s*pie/.test(e),"detector"),tE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./pieDiagram-IB7DONF6-CJylfmA9.js");return{diagram:t}},__vite__mapDeps([23,18,20,6,7,8,9,10,11,2,4,5]));return{id:ey,diagram:e}},"loader"),eE={id:ey,detector:JB,loader:tE},ry="quadrantChart",rE=g(e=>/^\s*quadrantChart/.test(e),"detector"),iE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./quadrantDiagram-7GDLP6J5-DzEP7PZZ.js");return{diagram:t}},__vite__mapDeps([24,6,7,8,9,10,11]));return{id:ry,diagram:e}},"loader"),nE={id:ry,detector:rE,loader:iE},aE=nE,iy="xychart",sE=g(e=>/^\s*xychart-beta/.test(e),"detector"),oE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./xychartDiagram-VJFVF3MP-C0MyBtk4.js");return{diagram:t}},__vite__mapDeps([25,6,7,8,9,10,11]));return{id:iy,diagram:e}},"loader"),lE={id:iy,detector:sE,loader:oE},cE=lE,ny="requirement",hE=g(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),uE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./requirementDiagram-KVF5MWMF-DhduwYYL.js");return{diagram:t}},__vite__mapDeps([26,15,6,7,8,9,10,11]));return{id:ny,diagram:e}},"loader"),fE={id:ny,detector:hE,loader:uE},dE=fE,ay="sequence",pE=g(e=>/^\s*sequenceDiagram/.test(e),"detector"),gE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./sequenceDiagram-X6HHIX6F-BdyORR2w.js");return{diagram:t}},__vite__mapDeps([27,13,19,6,7,8,9,10,11]));return{id:ay,diagram:e}},"loader"),mE={id:ay,detector:pE,loader:gE},yE=mE,sy="class",xE=g((e,t)=>{var r;return((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e)},"detector"),bE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./classDiagram-GIVACNV2-CEOXQUJC.js");return{diagram:t}},__vite__mapDeps([28,29,15,6,7,8,9,10,11]));return{id:sy,diagram:e}},"loader"),_E={id:sy,detector:xE,loader:bE},CE=_E,oy="classDiagram",wE=g((e,t)=>{var r;return/^\s*classDiagram/.test(e)&&((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e)},"detector"),kE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./classDiagram-v2-COTLJTTW-CEOXQUJC.js");return{diagram:t}},__vite__mapDeps([30,29,15,6,7,8,9,10,11]));return{id:oy,diagram:e}},"loader"),vE={id:oy,detector:wE,loader:kE},SE=vE,ly="state",TE=g((e,t)=>{var r;return((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e)},"detector"),ME=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./stateDiagram-DGXRK772-CayFiP2j.js");return{diagram:t}},__vite__mapDeps([31,32,15,1,2,3,4,6,7,8,9,10,11]));return{id:ly,diagram:e}},"loader"),AE={id:ly,detector:TE,loader:ME},LE=AE,cy="stateDiagram",BE=g((e,t)=>{var r;return!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper")},"detector"),EE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./stateDiagram-v2-YXO3MK2T-Dc8Qo_WT.js");return{diagram:t}},__vite__mapDeps([33,32,15,6,7,8,9,10,11]));return{id:cy,diagram:e}},"loader"),FE={id:cy,detector:BE,loader:EE},$E=FE,hy="journey",DE=g(e=>/^\s*journey/.test(e),"detector"),OE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./journeyDiagram-U35MCT3I-WaCoTE8u.js");return{diagram:t}},__vite__mapDeps([34,13,6,7,8,9,10,11]));return{id:hy,diagram:e}},"loader"),RE={id:hy,detector:DE,loader:OE},IE=RE,PE=g((e,t,r)=>{R.debug(`rendering svg for syntax error
`);const i=VL(t),n=i.append("g");i.attr("viewBox","0 0 2412 512"),Cf(i,100,512,!0),n.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),n.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),n.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),n.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),n.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),n.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),n.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),n.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),uy={draw:PE},NE=uy,zE={db:{},renderer:uy,parser:{parse:g(()=>{},"parse")}},WE=zE,fy="flowchart-elk",qE=g((e,t={})=>{var r;return/^\s*flowchart-elk/.test(e)||/^\s*flowchart|graph/.test(e)&&((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="elk"?(t.layout="elk",!0):!1},"detector"),HE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BdHPhGfE.js");return{diagram:t}},__vite__mapDeps([14,15,6,7,8,9,10,11]));return{id:fy,diagram:e}},"loader"),UE={id:fy,detector:qE,loader:HE},YE=UE,dy="timeline",jE=g(e=>/^\s*timeline/.test(e),"detector"),GE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./timeline-definition-BDJGKUSR-BDjmla0M.js");return{diagram:t}},__vite__mapDeps([35,6,7,8,9,10,11]));return{id:dy,diagram:e}},"loader"),VE={id:dy,detector:jE,loader:GE},XE=VE,py="mindmap",ZE=g(e=>/^\s*mindmap/.test(e),"detector"),KE=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./mindmap-definition-ALO5MXBD-Du6ihiNd.js");return{diagram:t}},__vite__mapDeps([36,37,7,6,8,9,10,11]));return{id:py,diagram:e}},"loader"),QE={id:py,detector:ZE,loader:KE},JE=QE,gy="kanban",tF=g(e=>/^\s*kanban/.test(e),"detector"),eF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./kanban-definition-NDS4AKOZ-BBaWRXqD.js");return{diagram:t}},__vite__mapDeps([38,6,7,8,9,10,11]));return{id:gy,diagram:e}},"loader"),rF={id:gy,detector:tF,loader:eF},iF=rF,my="sankey",nF=g(e=>/^\s*sankey-beta/.test(e),"detector"),aF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./sankeyDiagram-QLVOVGJD-CdgNy4-D.js");return{diagram:t}},__vite__mapDeps([39,6,7,8,9,10,11]));return{id:my,diagram:e}},"loader"),sF={id:my,detector:nF,loader:aF},oF=sF,yy="packet",lF=g(e=>/^\s*packet-beta/.test(e),"detector"),cF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./diagram-VNBRO52H-CE0NmFgN.js");return{diagram:t}},__vite__mapDeps([40,18,20,6,7,8,9,10,11,2,4,5]));return{id:yy,diagram:e}},"loader"),hF={id:yy,detector:lF,loader:cF},xy="radar",uF=g(e=>/^\s*radar-beta/.test(e),"detector"),fF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./diagram-SSKATNLV-CQi1uaEl.js");return{diagram:t}},__vite__mapDeps([41,18,20,6,7,8,9,10,11,2,4,5]));return{id:xy,diagram:e}},"loader"),dF={id:xy,detector:uF,loader:fF},by="block",pF=g(e=>/^\s*block-beta/.test(e),"detector"),gF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./blockDiagram-JOT3LUYC-j6QY8oGG.js");return{diagram:t}},__vite__mapDeps([42,5,2,1,6,7,8,9,10,11]));return{id:by,diagram:e}},"loader"),mF={id:by,detector:pF,loader:gF},yF=mF,_y="architecture",xF=g(e=>/^\s*architecture/.test(e),"detector"),bF=g(async()=>{const{diagram:e}=await _t(async()=>{const{diagram:t}=await import("./architectureDiagram-IEHRJDOE-By1r-_vc.js");return{diagram:t}},__vite__mapDeps([43,18,19,20,6,7,8,9,10,11,2,4,5,37]));return{id:_y,diagram:e}},"loader"),_F={id:_y,detector:xF,loader:bF},CF=_F,Gu=!1,qs=g(()=>{Gu||(Gu=!0,Sa("error",WE,e=>e.toLowerCase().trim()==="error"),Sa("---",{db:{clear:g(()=>{},"clear")},styles:{},renderer:{draw:g(()=>{},"draw")},parser:{parse:g(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:g(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),lf(LB,iF,SE,CE,WB,XB,QB,eE,dE,yE,YE,IB,$B,JE,XE,YB,$E,LE,IE,aE,oF,hF,cE,yF,CF,dF))},"addDiagrams"),wF=g(async()=>{R.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(gi).map(async([r,{detector:i,loader:n}])=>{if(n)try{Fo(r)}catch{try{const{diagram:a,id:o}=await n();Sa(o,a,i)}catch(a){throw R.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete gi[r],a}}}))).filter(r=>r.status==="rejected");if(t.length>0){R.error(`Failed to load ${t.length} external diagrams`);for(const r of t)R.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),kF="graphics-document document";function Cy(e,t){e.attr("role",kF),t!==""&&e.attr("aria-roledescription",t)}g(Cy,"setA11yDiagramInfo");function wy(e,t,r,i){if(e.insert!==void 0){if(r){const n=`chart-desc-${i}`;e.attr("aria-describedby",n),e.insert("desc",":first-child").attr("id",n).text(r)}if(t){const n=`chart-title-${i}`;e.attr("aria-labelledby",n),e.insert("title",":first-child").attr("id",n).text(t)}}}g(wy,"addSVGa11yTitleDescription");var Mr,vl=(Mr=class{constructor(t,r,i,n,a){this.type=t,this.text=r,this.db=i,this.parser=n,this.renderer=a}static async fromText(t,r={}){var l,h;const i=he(),n=Ml(t,i);t=UM(t)+`
`;try{Fo(n)}catch{const u=Sx(n);if(!u)throw new of(`Diagram ${n} not found.`);const{id:f,diagram:d}=await u();Sa(f,d)}const{db:a,parser:o,renderer:s,init:c}=Fo(n);return o.parser&&(o.parser.yy=a),(l=a.clear)==null||l.call(a),c==null||c(i),r.title&&((h=a.setDiagramTitle)==null||h.call(a,r.title)),await o.parse(t),new Mr(n,t,a,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},g(Mr,"Diagram"),Mr),Vu=[],vF=g(()=>{Vu.forEach(e=>{e()}),Vu=[]},"attachFunctions"),SF=g(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function ky(e){const t=e.match(sf);if(!t)return{text:e,metadata:{}};let r=v1(t[1],{schema:k1})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}g(ky,"extractFrontMatter");var TF=g(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),MF=g(e=>{const{text:t,metadata:r}=ky(e),{displayMode:i,title:n,config:a={}}=r;return i&&(a.gantt||(a.gantt={}),a.gantt.displayMode=i),{title:n,config:a,text:t}},"processFrontmatter"),AF=g(e=>{const t=De.detectInit(e)??{},r=De.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):(r==null?void 0:r.type)==="wrap"&&(t.wrap=!0),{text:EM(e),directive:t}},"processDirectives");function Ec(e){const t=TF(e),r=MF(t),i=AF(r.text),n=pc(r.config,i.directive);return e=SF(i.text),{code:e,title:r.title,config:n}}g(Ec,"preprocessDiagram");function vy(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}g(vy,"toBase64");var LF=5e4,BF="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",EF="sandbox",FF="loose",$F="http://www.w3.org/2000/svg",DF="http://www.w3.org/1999/xlink",OF="http://www.w3.org/1999/xhtml",RF="100%",IF="100%",PF="border:0;margin:0;",NF="margin:0",zF="allow-top-navigation-by-user-activation allow-popups",WF='The "iframe" tag is not supported by your browser.',qF=["foreignobject"],HF=["dominant-baseline"];function Fc(e){const t=Ec(e);return ka(),Wx(t.config??{}),t}g(Fc,"processAndSetConfigs");async function Sy(e,t){qs();try{const{code:r,config:i}=Fc(e);return{diagramType:(await My(r)).type,config:i}}catch(r){if(t!=null&&t.suppressErrors)return!1;throw r}}g(Sy,"parse");var Xu=g((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),UF=g((e,t=new Map)=>{var i;let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const s=e.htmlLabels??((i=e.flowchart)==null?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(c=>{ju(c.styles)||s.forEach(l=>{r+=Xu(c.id,l,c.styles)}),ju(c.textStyles)||(r+=Xu(c.id,"tspan",((c==null?void 0:c.textStyles)||[]).map(l=>l.replace("color","fill"))))})}return r},"createCssStyles"),YF=g((e,t,r,i)=>{const n=UF(e,r),a=sb(t,n,e.themeVariables);return bl(cB(`${i}{${a}}`),uB)},"createUserStyles"),jF=g((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=qr(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),GF=g((e="",t)=>{var n,a;const r=(a=(n=t==null?void 0:t.viewBox)==null?void 0:n.baseVal)!=null&&a.height?t.viewBox.baseVal.height+"px":IF,i=vy(`<body style="${NF}">${e}</body>`);return`<iframe style="width:${RF};height:${r};${PF}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${zF}">
  ${WF}
</iframe>`},"putIntoIFrame"),Zu=g((e,t,r,i,n)=>{const a=e.append("div");a.attr("id",r),i&&a.attr("style",i);const o=a.append("svg").attr("id",t).attr("width","100%").attr("xmlns",$F);return n&&o.attr("xmlns:xlink",n),o.append("g"),e},"appendDivSvgG");function Sl(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}g(Sl,"sandboxedIframe");var VF=g((e,t,r,i)=>{var n,a,o;(n=e.getElementById(t))==null||n.remove(),(a=e.getElementById(r))==null||a.remove(),(o=e.getElementById(i))==null||o.remove()},"removeExistingElements"),XF=g(async function(e,t,r){var I,E,A,F,L,D;qs();const i=Fc(t);t=i.code;const n=he();R.debug(n),t.length>((n==null?void 0:n.maxTextSize)??LF)&&(t=BF);const a="#"+e,o="i"+e,s="#"+o,c="d"+e,l="#"+c,h=g(()=>{const W=pt(f?s:l).node();W&&"remove"in W&&W.remove()},"removeTempElements");let u=pt("body");const f=n.securityLevel===EF,d=n.securityLevel===FF,p=n.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),f){const B=Sl(pt(r),o);u=pt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=pt(r);Zu(u,e,c,`font-family: ${p}`,DF)}else{if(VF(document,e,c,o),f){const B=Sl(pt("body"),o);u=pt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=pt("body");Zu(u,e,c)}let m,y;try{m=await vl.fromText(t,{title:i.title})}catch(B){if(n.suppressErrorRendering)throw h(),B;m=await vl.fromText("error"),y=B}const x=u.select(l).node(),b=m.type,C=x.firstChild,v=C.firstChild,k=(E=(I=m.renderer).getClasses)==null?void 0:E.call(I,t,m),_=YF(n,b,k,a),w=document.createElement("style");w.innerHTML=_,C.insertBefore(w,v);try{await m.renderer.draw(t,e,Pu.version,m)}catch(B){throw n.suppressErrorRendering?h():NE.draw(t,e,Pu.version),B}const O=u.select(`${l} svg`),N=(F=(A=m.db).getAccTitle)==null?void 0:F.call(A),$=(D=(L=m.db).getAccDescription)==null?void 0:D.call(L);Ay(b,O,N,$),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",OF);let S=u.select(l).node().innerHTML;if(R.debug("config.arrowMarkerAbsolute",n.arrowMarkerAbsolute),S=jF(S,f,$t(n.arrowMarkerAbsolute)),f){const B=u.select(l+" svg").node();S=GF(S,B)}else d||(S=pi.sanitize(S,{ADD_TAGS:qF,ADD_ATTR:HF,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(vF(),y)throw y;return h(),{diagramType:b,svg:S,bindFunctions:m.db.bindFunctions}},"render");function Ty(e={}){var i;const t=Ht({},e);t!=null&&t.fontFamily&&!((i=t.themeVariables)!=null&&i.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),Nx(t),t!=null&&t.theme&&t.theme in Ze?t.themeVariables=Ze[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Ze.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?Px(t):pf();Tl(r.logLevel),qs()}g(Ty,"initialize");var My=g((e,t={})=>{const{code:r}=Ec(e);return vl.fromText(r,t)},"getDiagramFromText");function Ay(e,t,r,i){Cy(t,e),wy(t,r,i,t.attr("id"))}g(Ay,"addA11yInfo");var Or=Object.freeze({render:XF,parse:Sy,getDiagramFromText:My,initialize:Ty,getConfig:he,setConfig:gf,getSiteConfig:pf,updateSiteConfig:zx,reset:g(()=>{ka()},"reset"),globalReset:g(()=>{ka(mi)},"globalReset"),defaultConfig:mi});Tl(he().logLevel);ka(he());var ZF=g((e,t,r)=>{R.warn(e),dc(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),Ly=g(async function(e={querySelector:".mermaid"}){try{await KF(e)}catch(t){if(dc(t)&&R.error(t.str),fe.parseError&&fe.parseError(t),!e.suppressErrors)throw R.error("Use the suppressErrors option to suppress these errors"),t}},"run"),KF=g(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=Or.getConfig();R.debug(`${e?"":"No "}Callback function found`);let n;if(r)n=r;else if(t)n=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");R.debug(`Found ${n.length} diagrams`),(i==null?void 0:i.startOnLoad)!==void 0&&(R.debug("Start On Load: "+(i==null?void 0:i.startOnLoad)),Or.updateSiteConfig({startOnLoad:i==null?void 0:i.startOnLoad}));const a=new De.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const c of Array.from(n)){if(R.info("Rendering diagram: "+c.id),c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");const l=`mermaid-${a.next()}`;o=c.innerHTML,o=im(De.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=De.detectInit(o);h&&R.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:f}=await $y(l,o,c);c.innerHTML=u,e&&await e(l),f&&f(c)}catch(u){ZF(u,s,fe.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),By=g(function(e){Or.initialize(e)},"initialize"),QF=g(async function(e,t,r){R.warn("mermaid.init is deprecated. Please use run instead."),e&&By(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await Ly(i)},"init"),JF=g(async(e,{lazyLoad:t=!0}={})=>{qs(),lf(...e),t===!1&&await wF()},"registerExternalDiagrams"),Ey=g(function(){if(fe.startOnLoad){const{startOnLoad:e}=Or.getConfig();e&&fe.run().catch(t=>R.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",Ey,!1);var t3=g(function(e){fe.parseError=e},"setParseErrorHandler"),us=[],To=!1,Fy=g(async()=>{if(!To){for(To=!0;us.length>0;){const e=us.shift();if(e)try{await e()}catch(t){R.error("Error executing queue",t)}}To=!1}},"executeQueue"),e3=g(async(e,t)=>new Promise((r,i)=>{const n=g(()=>new Promise((a,o)=>{Or.parse(e,t).then(s=>{a(s),r(s)},s=>{var c;R.error("Error parsing",s),(c=fe.parseError)==null||c.call(fe,s),o(s),i(s)})}),"performCall");us.push(n),Fy().catch(i)}),"parse"),$y=g((e,t,r)=>new Promise((i,n)=>{const a=g(()=>new Promise((o,s)=>{Or.render(e,t,r).then(c=>{o(c),i(c)},c=>{var l;R.error("Error parsing",c),(l=fe.parseError)==null||l.call(fe,c),s(c),n(c)})}),"performCall");us.push(a),Fy().catch(n)}),"render"),fe={startOnLoad:!0,mermaidAPI:Or,parse:e3,render:$y,init:QF,run:Ly,registerExternalDiagrams:JF,registerLayoutLoaders:W0,initialize:By,parseError:void 0,contentLoaded:Ey,setParseErrorHandler:t3,detectType:Ml,registerIconPacks:G1},T3=fe;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */export{ma as $,l3 as A,a3 as B,ln as C,kx as D,gb as E,pc as F,he as G,ff as H,RM as I,k1 as J,VL as K,Pu as L,$s as M,m3 as N,Up as O,y3 as P,Ex as Q,uk as R,rk as S,MA as T,qv as U,xi as V,s3 as W,Al as X,ch as Y,MM as Z,g as _,cb as a,Dg as a$,OM as a0,An as a1,ib as a2,Mn as a3,Y as a4,at as a5,wf as a6,tL as a7,N0 as a8,w3 as a9,c3 as aA,d3 as aB,Vy as aC,f3 as aD,Qp as aE,tc as aF,Ss as aG,bk as aH,xk as aI,vi as aJ,yk as aK,mk as aL,Ya as aM,Fn as aN,Ql as aO,Kl as aP,ti as aQ,Ua as aR,u3 as aS,g3 as aT,Nr as aU,vM as aV,Og as aW,As as aX,Fs as aY,es as aZ,Ig as a_,S1 as aa,$t as ab,dr as ac,Wl as ad,dm as ae,qr as af,Hg as ag,YL as ah,C3 as ai,k3 as aj,b3 as ak,tt as al,_3 as am,ML as an,kL as ao,wL as ap,kM as aq,gT as ar,mM as as,ac as at,ju as au,xs as av,G1 as aw,j1 as ax,p3 as ay,h3 as az,lb as b,tM as b0,wM as b1,gM as b2,rT as b3,sc as b4,ZT as b5,TM as b6,Dn as b7,Li as b8,Qa as b9,oM as ba,gB as bb,$n as bc,ts as bd,eM as be,Ag as bf,aT as bg,sT as bh,_r as bi,yu as bj,oT as bk,oc as bl,nT as bm,hT as bn,Bi as bo,fr as bp,fu as bq,lc as br,Bg as bs,wl as bt,SM as bu,Es as bv,yt as c,pt as d,Cf as e,Ht as f,ub as g,er as h,Ar as i,K1 as j,Ai as k,R as l,T3 as m,Yg as n,o3 as o,S3 as p,fb as q,v3 as r,hb as s,db as t,De as u,v1 as v,NM as w,bL as x,x3 as y,ob as z};

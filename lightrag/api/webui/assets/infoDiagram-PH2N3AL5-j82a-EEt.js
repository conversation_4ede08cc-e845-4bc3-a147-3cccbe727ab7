import{_ as e,l as o,K as i,e as n,L as p}from"./mermaid-vendor-Bi3TzHdn.js";import{p as m}from"./radar-MK3ICKWK-CxtTBuu1.js";import"./feature-graph-ChwGSzXI.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";import"./_baseUniq-CvCjC6qE.js";import"./_basePickBy-DY0K0qir.js";import"./clone-CFB5BEXb.js";var g={parse:e(async r=>{const a=await m("info",r);o.debug(a)},"parse")},v={version:p.version},d=e(()=>v.version,"getVersion"),c={getVersion:d},l=e((r,a,s)=>{o.debug(`rendering info diagram
`+r);const t=i(a);n(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${s}`)},"draw"),f={draw:l},L={parser:g,db:c,renderer:f};export{L as diagram};

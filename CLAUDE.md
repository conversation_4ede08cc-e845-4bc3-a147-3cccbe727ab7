# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Python Core Package
```bash
# Install in development mode
pip install -e .

# Install with API dependencies  
pip install -e ".[api]"

# Run tests
python -m pytest tests/

# Run specific test
python -m pytest tests/test_lightrag_ollama_chat.py

# Run examples
cd examples/
python lightrag_openai_demo.py
```

### Web UI Frontend
```bash
cd lightrag_webui/

# Development (with Bun - preferred)
bunx --bun vite

# Development (with Node.js)
vite

# Build
bunx --bun vite build

# Lint
eslint .
```

### API Server
```bash
# Start LightRAG server
lightrag-server

# Start with Gunicorn
lightrag-gunicorn

# Docker deployment
docker compose up
```

## Architecture Overview

### Core Components

**LightRAG Core** (`lightrag/lightrag.py`)
- Main RAG orchestration engine
- Handles document indexing and querying
- Supports multiple storage backends
- Requires explicit initialization: `await rag.initialize_storages()` and `await initialize_pipeline_status()`

**Storage Layer** (`lightrag/kg/`)
- **Vector Storage**: NanoVectorDB (default), Milvus, Qdrant, Faiss, PostgreSQL, MongoDB
- **Graph Storage**: NetworkX (default), Neo4j, PostgreSQL AGE, Memgraph  
- **KV Storage**: JSON files (default), PostgreSQL, Redis, MongoDB
- **Document Status**: JSON (default), PostgreSQL, MongoDB

**LLM Integration** (`lightrag/llm/`)
- OpenAI, Azure OpenAI, Anthropic, Gemini
- Ollama, Hugging Face, LlamaIndex
- AWS Bedrock, NVIDIA, SiliconCloud
- Configurable via embedding and LLM model functions

**Query Processing** (`lightrag/operate.py`)
- Entity extraction and knowledge graph construction
- Multiple query modes: local, global, hybrid, naive, mix
- Chunking, embedding, and retrieval operations

### API Server (`lightrag/api/`)
- FastAPI-based REST API
- Web UI for document management and graph visualization
- Ollama-compatible chat interface
- Authentication and multi-user support

### Web UI (`lightrag_webui/`)
- React + TypeScript frontend
- Built with Vite and Tailwind CSS
- Features: document upload, graph visualization, query interface
- Uses Zustand for state management

## Key Concepts

**Initialization Pattern**
```python
rag = LightRAG(working_dir="./storage", ...)
await rag.initialize_storages()  # Required!
await initialize_pipeline_status()  # Required!
```

**Storage Selection**
- Set storage types during LightRAG initialization
- Each storage type has multiple implementations
- Workspace parameter provides data isolation

**Query Modes**
- `local`: Context-dependent information
- `global`: Global knowledge utilization  
- `hybrid`: Combines local and global
- `naive`: Basic vector search
- `mix`: Integrates knowledge graph and vector retrieval

**Document Processing**
- Documents are chunked by token size (default 1200 tokens)
- Entities and relationships extracted via LLM
- Knowledge graph constructed automatically
- Vector embeddings generated for retrieval

## Environment Configuration

Key environment variables:
- `OPENAI_API_KEY`: OpenAI API access
- `WORKING_DIR`: Storage directory
- `TOP_K`: Number of top items to retrieve (default 60)
- `MAX_TOKENS`: Maximum tokens for LLM summary (default 32000)
- `MAX_ASYNC`: Maximum concurrent LLM processes (default 4)
- Storage-specific: `NEO4J_URI`, `POSTGRES_URL`, `REDIS_URL`, etc.

## Testing and Examples

**Test Files**
- `tests/test_graph_storage.py`: Graph storage implementation tests
- `tests/test_lightrag_ollama_chat.py`: Ollama integration tests

**Example Usage**
- `examples/lightrag_openai_demo.py`: Basic OpenAI integration
- `examples/lightrag_ollama_demo.py`: Ollama model usage  
- `examples/graph_visual_*.py`: Graph visualization examples
- `examples/rerank_example.py`: Reranking functionality

## Deployment Options

**Local Development**: Use JSON-based storage (default)
**Production**: PostgreSQL for unified storage or specialized databases (Neo4j for graphs, Milvus for vectors)
**Containerized**: Docker Compose with database services
**Kubernetes**: Helm charts available in `k8s-deploy/`

## Common Patterns

**Multi-modal Integration**: Use RAG-Anything for document parsing and multimodal RAG
**Conversation History**: Pass conversation context via QueryParam
**Custom Models**: Implement embedding_func and llm_model_func for any provider
**Data Export**: Built-in export to CSV, Excel, Markdown formats
**Entity Management**: Create, edit, merge, and delete entities/relationships programmatically